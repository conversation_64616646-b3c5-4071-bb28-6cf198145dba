@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, brandColorThree, fifteenSpace, grayBorder from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.main_container {
  background: colorBlack;
  padding: 40px 124px;

  @media (max-width: breakpoint-md) {
    padding: 40px 91px;
  }

  @media (max-width: breakpoint-sm-450) {
    padding: 40px 12px;
  }
}

.first_second_row {
  display: flex;
  flex-direction: column;

  @media (max-width: breakpoint-md) {
    flex-direction: column-reverse;
  }
}

.column {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 0px;

  @media (max-width: breakpoint-md) {
    padding: 0px;
    /* gap: 0px; */
  }
}

/* height: auto; */

.firstrow {
  /* margin: 0px; */
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding-bottom: 20px;

  @media (max-width: breakpoint-md) {
    grid-template-columns: repeat(2, 1fr);
    padding-top: 20px;
    padding-bottom: 0px;
    border-top: 1px solid #646464;

    .column {
      padding: 24px 0px;
    }
  }

  @media (max-width: breakpoint-sm-450) {
    grid-template-columns: repeat(1, 1fr);
    padding-left: 55px;

    .column {
      padding: 8px 0px;
    }

    padding-bottom: 20px;
  }
}

.title_firstrow {
  text-decoration: none;
}

.title_firstrow h4 {
  color: colorWhite;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  /* list-style: none; */
  text-decoration: none;
}

.link_title {
  padding-left: 0px;
  display: grid;
  row-gap: 16px;

  .sublink_title {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    text-decoration: none;
    list-style: none;
    color: #cdcdcd;
  }
}

.link_title > li {
  list-style-type: none;
}

.secondrow {
  /* Assuming your grid container class is 'secondrow' */
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding-top: 20px;
  border-top: 1px solid #646464;

  @media (max-width: breakpoint-md) {
    grid-template-columns: repeat(2, 1fr);

    .sublink_title {
      display: none;
    }

    .column {
      gap: 0px;
    }

    padding: 24px 0px 44px 0px;
    border: none;
  }

  @media (max-width: breakpoint-sm-450) {
    grid-template-columns: repeat(1, 1fr);
    padding: 0 0 20px 55px;

    .title_firstrow {
      padding: 8px 0px;
    }
  }
}

.thirdrow {
  display: flex;
  justify-content: center;
  gap: 5px;
  padding: 20px 0px;
  text-align: center;
}

.terms_and_condition_title {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 14px;
  color: colorWhite;
  border-right: 1px solid white;
  padding-right: 5px;
  text-decoration: none;
}

.terms_and_condition_title:last-child {
  border-right: none;
  /* Remove border for last element */
  padding-right: 0;
  /* Remove padding for last element */
}

.company_logo_section {
  display: flex;
  justify-content: space-between;
  padding: 20px 0px;
  border-top: 1px solid #646464;

  .iconsContainer {
    display: flex;
    gap: 10px;
  }

  @media (max-width: breakpoint-sm-550) {
    flex-direction: column;
    gap: 10px;

    .iconsContainer {
      justify-content: center;
    }

    .imageContainer {
      display: flex;
      justify-content: center;
    }
  }
}

.copyright {
  display: flex;
  justify-content: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  color: colorWhite;
  text-align: center;
}

.icon {
  border-radius: 8px;
}
