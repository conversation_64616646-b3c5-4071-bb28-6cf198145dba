{"name": "mtl-nextjs-backend", "version": "1.0.0", "private": true, "description": "Backend services for the mtl-nextjs-aws-site project", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "validate-config": "node scripts/validateConfig.js", "prestart": "npm run validate-config", "build": "next build"}, "dependencies": {"@aws-sdk/client-ssm": "^3.835.0", "@aws-sdk/types": "^3.821.0", "axios": "^1.7.7", "dotenv": "^16.0.3", "express": "^4.18.2"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}