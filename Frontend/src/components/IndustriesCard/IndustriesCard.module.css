@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-sm-320, breakpoint-sm-427, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px, breakpoint-xl-2100, breakpoint-md-769,breakpoint-sm, breakpoint-sm-430, breakpoint-sm-390, breakpoint-sm-326, breakpoint-md-850, breakpoint-xl, breakpoint-xl-1439, breakpoint-xl-1400, breakpoint-xl-2000, breakpoint-xl-2559, breakpoint-xl-2560 from breakpoints;

/* Small screens Styles <577px (Embla-Carousel) */
.embla {
  overflow: hidden;
  background-color: colorBlack;
}

.embla__container {
  display: flex;
}

.embla__slide {
  flex: 0 0 100%;
  min-width: 0;
  opacity: 0;
  transition: opacity ease-in-out;

  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center;
  padding: 149px 124px 80px 124px;

  @media (min-width: breakpoint-xl-2000) {
    padding: 174px 150px 80px 150px;
  }

  @media (max-width: breakpoint-md) {
    padding-left: 32px;
    padding-right: 32px;
  }
}

.embla__slide:active {
  opacity: 1;
}

.embla__slide_mobile_container {
  height: 474px;
  flex: 0 0 100%;
  min-width: 0;
  color: colorWhite;
  text-decoration: none;
}

.embla__slide_mobile {
  display: block;
  position: fixed;
  left: 0;
  right: 0;
  height: 404px;
  background: #00000070;
  align-content: end;
  z-index: 11;
}

.mobile_box_link {
  display: block;
  height: 402px;
}

.embla__slide_mobileOne {
  display: block;
  position: fixed;
  left: 0;
  right: 0;
  height: 404px;
  background-image: var(--first-box-bg-image);
  background-position: center;
  background-size: cover;
  z-index: 1;
}

.embla__slide_mobileTwo {
  display: block;
  position: fixed;
  left: 0;
  right: 0;
  height: 404px;
  background-image: var(--second-box-bg-image);
  background-position: center;
  background-size: cover;
  z-index: 1;
}

.embla__slide_mobileThree {
  display: block;
  position: fixed;
  left: 0;
  right: 0;
  height: 404px;
  background-image: var(--third-box-bg-image);
  background-position: center;
  background-size: cover;
  z-index: 1;
}

.titleForMobile {
  left: 56px !important;
}

.contentWrapperForMobile {
  display: flex;
  flex-direction: column;
  margin: 40px 16px;
}

.embla__controls {
  display: flex;
}

.mobileDots {
  display: block;
}

/* Big screens styles >576px */

.container {
  margin: 0;
  padding: 0;
  display: flex;
  position: relative;
  height: 596px;
  color: colorWhite;
  background-color: colorBlack;
  overflow-wrap: anywhere;
  background-image: var(--first-box-bg-image);
  background-position: center;
  background-size: cover;

  @media (max-width: breakpoint-md) {
    height: 396px;
  }
}

.title h2 {
  position: absolute;
  left: 124px;
  top: 56px;
  z-index: 1;
  font-size: 96px;
  font-weight: 600;
  line-height: 102%;
  z-index: 2;

  @media (min-width: breakpoint-xl-2560) {
    font-size: 120px;
  }

  @media (min-width: breakpoint-xl-1440) and (max-width: breakpoint-xl-2559) {
    font-size: 96px;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1439) {
    font-size: 64px;
  }

  @media (max-width: breakpoint-md) {
    font-size: 32px;
    left: 71px;
    top: 35px;
  }
}

.box {
  display: flex;
  flex-direction: column;
  margin: 0 2px;
  width: 100%;
  background: #0000004d;
}

.box:hover {
  background: rgba(0, 0, 0, 0.7);

  .contentWrapper {
    animation: fadeInUp 0.25s linear;
    animation-timing-function: ease-in-out;
    animation-fill-mode: both;
  }

  .descAndButtonWrapper {
    display: block;
  }
}

@keyframes fadeInUp {
  from {
    transform: translate(0px, 100px);
    opacity: 0;
  }

  to {
    transform: translate(0px, 0px);
    opacity: 1;
  }
}

.box::before {
  position: absolute;
  content: '';
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  opacity: 0;
  background: #0000004d;
  background-position: center;
  background-size: cover;
  transition: all 0.25s ease-in-out;
}

.box:hover::before {
  opacity: 1;
}

.boxTwo::before {
  background-image: var(--second-box-bg-image);
}

.boxThree::before {
  background-image: var(--third-box-bg-image);
}

.boxOne {
  margin: 0 2px 0 0 !important;
}

.boxTwo:hover {
  .link {
    background: rgba(0, 0, 0, 0.7);
  }
}

.boxThree:hover {
  .link {
    background: rgba(0, 0, 0, 0.7);
  }
}

.boxThree {
  margin: 0 0 0 2px !important;
}

.link {
  z-index: 1;
  /* width: 100%; */
  height: 100%;
  padding: 0 2.25rem;
  align-content: center;
  text-decoration: none;
  background: #0000004d;

  @media (min-width: breakpoint-xl-2560) {
    padding: 0 8rem;
  }

  @media (min-width: breakpoint-xl-1400) and (max-width: breakpoint-xl-2559) {
    padding: 0 6rem;
  }

  @media (min-width: breakpoint-xl) and (max-width: breakpoint-xl-1439) {
    padding: 0 4rem;
  }

  @media (min-width: breakpoint-lg) and (max-width: breakpoint-xl-1199) {
    padding: 0 2.25rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 0 1.25rem;
  }
}

.contentWrapper {
  margin-top: 3rem;
}

.industriesCardsBoxTitle h3 {
  margin-bottom: 10px;
  font-size: 32px;
  font-style: normal;
  font-weight: 600;
  line-height: 132%;
  letter-spacing: -0.32px;

  @media (max-width: breakpoint-md) {
    font-size: 24px;
    line-height: 138%;
    letter-spacing: 0.48px;
  }
}

.descAndButtonWrapper {
  display: none;
}

.industriesCardsBoxDescription {
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
