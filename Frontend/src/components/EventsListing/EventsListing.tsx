import React from 'react';
import { Container } from 'react-bootstrap';
import Image from 'next/image'; // Assuming you're using Next.js Image component
import Link from 'next/link'; // Assuming you're using Next.js
import Heading from '@components/Heading';

import styles from './EventsListing.module.css'; // Update the path based on your styles

export default function EventsListing({ eventsListingBoxData }) {
  return (
    <Container fluid className={styles.eventsListing}>
      {eventsListingBoxData.map((data, index) => (
        <Link
          href={`/events/${data.attributes.slug}`}
          key={index}
          className={styles.link}
          prefetch={false}
        >
          <div className={styles.card_preview}>
            <Image
              loading="eager"
              priority
              key={
                data?.attributes?.hero_section?.hero_image?.data?.attributes
                  ?.url
              }
              src={
                data?.attributes?.hero_section?.hero_image?.data?.attributes
                  ?.format?.small?.url ||
                data?.attributes?.hero_section?.hero_image?.data?.attributes
                  ?.formats?.small?.url ||
                data?.attributes?.hero_section?.hero_image?.data?.attributes
                  ?.format?.medium?.url ||
                data?.attributes?.hero_section?.hero_image?.data?.attributes
                  ?.formats?.medium?.url ||
                data?.attributes?.hero_section?.hero_image?.data?.attributes
                  ?.url
              }
              alt={
                data?.attributes?.hero_section?.hero_image?.data?.attributes
                  ?.alternativeText
              }
              className={styles.previewImage}
              width={384}
              height={220}
            />
            <div className={styles.text_container}>
              <div className={styles.industry_wrapper}>
                <div className={styles.industry_title} key={index}>
                  Event Date : {data?.attributes?.hero_section?.date_value}
                </div>
              </div>
              <div className={styles.industry_wrapper}>
                <div className={styles.industry_title} key={index}>
                  {data?.attributes?.hero_section?.venue_value}
                </div>
              </div>
            </div>
            <Heading
              headingType="h2"
              title={data?.attributes?.hero_section?.hero_title}
              className={styles.card_title}
            />
          </div>
        </Link>
      ))}
    </Container>
  );
}
