'use client';

import React from 'react';
import { Accordion, Container } from 'react-bootstrap';
import classNames from '@utils/classNames';
import Heading from '@components/Heading';

import styles from './Faq.module.css';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function Faq(faqData: any) {
  type FaqDataDataTypes = {
    id: number;
    question: string;
    answer: string;
  };

  // eslint-disable-next-line react/destructuring-assignment
  const { title } = faqData.faqData;

  return (
    <Container fluid className={styles.container}>
      <Heading headingType="h2" title={title} className={styles.title} />
      <div className={styles.faqsWrapper}>
        <Accordion bsPrefix={styles.accordion}>
          {faqData?.faqData?.faq_items?.map((data: FaqDataDataTypes) => (
            <Accordion.Item
              eventKey={`${data?.id}`}
              bsPrefix={styles.accordion__item}
              key={data?.id}
            >
              <Accordion.Header
                as="h3"
                className={classNames(
                  styles.accordion__header,
                  'accordionIcon',
                )}
              >
                {data?.question}
              </Accordion.Header>
              <Accordion.Body
                bsPrefix={styles.accordion__body}
                dangerouslySetInnerHTML={{
                  __html: data?.answer,
                }}
              />
            </Accordion.Item>
          ))}
        </Accordion>
      </div>
    </Container>
  );
}
