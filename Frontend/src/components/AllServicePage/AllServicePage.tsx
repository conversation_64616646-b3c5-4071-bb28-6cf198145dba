'use client';
import React from 'react';
import { Container } from 'react-bootstrap';
import styles from './AllServicePage.module.css';
import Heading from '@components/Heading';
import Link from 'next/link';
import Image from 'next/image';
import ImageWithBlurPreview from '@components/ImageWithBlurPreview';

export default function AllServicePage({ allServicePageData }) {
  return (
    <>
      <Container fluid className={styles.main_container}>
        {allServicePageData?.map((serviceData, index) => {
          if (index % 2 === 0) {
            return (
              <div className={styles.vision_container} key={index}>
                <div className={styles.image_container}>
                  <ImageWithBlurPreview
                    data={serviceData?.image?.data?.attributes}
                    width={720}
                    height={500}
                    quality={100}
                    priority={true}
                    loading="eager"
                    mainClass={styles.image}
                  />
                </div>
                <div className={styles.box_container}>
                  <div className={styles.right_container}>
                    <div className={styles.card__number}>
                      {serviceData?.tag}
                    </div>
                    <Heading
                      headingType="h2"
                      title={serviceData?.title}
                      className={styles.title}
                    />
                    <div
                      className={styles.richText}
                      dangerouslySetInnerHTML={{
                        __html: serviceData?.description,
                      }}
                    ></div>
                  </div>
                  <div className={styles.subheading}>
                    {serviceData?.button?.title}
                  </div>
                  <div className={styles.arrow_pages_container}>
                    {serviceData?.l_2_service_pages?.data.map((data, index) => {
                      return (
                        <Link
                          href={`/services/${data?.attributes?.slug}`}
                          className={styles.arrow_service_page}
                          key={index}
                          prefetch={false}
                        >
                          <Image
                            src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/arrow_right_gradient_bf42e724af_d3b8061b36.svg`}
                            alt="Arrow right"
                            width={24}
                            height={24}
                          />
                          <div className={styles.service_page_title}>
                            {data?.attributes?.pageName}
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          } else {
            return (
              <div className={styles.mission_container} key={index}>
                <div className={styles.image_container}>
                  <ImageWithBlurPreview
                    data={serviceData?.image?.data?.attributes}
                    width={720}
                    height={500}
                    quality={100}
                    priority={true}
                    loading="eager"
                    mainClass={styles.image}
                  />
                </div>
                <div className={styles.box_container}>
                  <div className={styles.right_container}>
                    <div className={styles.card__number}>
                      {serviceData?.tag}
                    </div>
                    <Heading
                      headingType="h2"
                      title={serviceData?.title}
                      className={styles.title}
                    />
                    <div
                      className={styles.richText}
                      dangerouslySetInnerHTML={{
                        __html: serviceData?.description,
                      }}
                    ></div>
                  </div>
                  <div className={styles.subheading}>
                    {serviceData?.button?.title}
                  </div>
                  <div className={styles.arrow_pages_container}>
                    {serviceData?.l_2_service_pages?.data.map((data, index) => {
                      return (
                        <Link
                          href={`/services/${data?.attributes?.slug}`}
                          className={styles.arrow_service_page}
                          key={index}
                          prefetch={false}
                        >
                          <Image
                            src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/arrow_right_gradient_bf42e724af_d3b8061b36.svg`}
                            alt="Arrow right"
                            width={24}
                            height={24}
                          />
                          <div className={styles.service_page_title}>
                            {data?.attributes?.pageName}
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          }
        })}
      </Container>
    </>
  );
}
