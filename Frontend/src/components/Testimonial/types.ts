export interface TestimonialSliderType {
  clientDescription: string;
  clientName: string;
  id: number;
  image: {
    data: {
      id: number;
      attributes: {
        alternativeText: string;
        caption: string;
        createdAt: string;
        ext: string;
        formats: null;
        hash: string;
        height: number;
        mime: string;
        name: string;
        previewUrl: null;
        provider: string;
        provider_metadata: null;
        size: number;
        updatedAt: string;
        url: string;
        width: number;
      };
    };
  };
  testimonial_video_link: string;
}

export interface TestimonialTypes {
  data: {
    id: number;
    testimonial_playbtn_logo: {
      data: {
        id: number;
        attributes: {
          alternativeText: string;
          caption: string;
          createdAt: string;
          ext: string;
          formats: null;
          hash: string;
          height: number;
          mime: string;
          name: string;
          previewUrl: null;
          provider: string;
          provider_metadata: null;
          size: number;
          updatedAt: string;
          url: string;
          width: number;
        };
      };
    };
    circular_text_line_svg: {
      data: {
        id: number;
        attributes: {
          alternativeText: string;
          caption: string;
          createdAt: string;
          ext: string;
          formats: null;
          hash: string;
          height: number;
          mime: string;
          name: string;
          previewUrl: null;
          provider: string;
          provider_metadata: null;
          size: number;
          updatedAt: string;
          url: string;
          width: number;
        };
      };
    };
    testimonials_slider: TestimonialSliderType[];
    title: string;
    tagline_url: string;

  };
}
