import React from 'react';
import { Container } from 'react-bootstrap';
import Heading from '@components/Heading';
import classNames from '@utils/classNames';
import variables from '@styles/variables.module.css';

import styles from './RichText.module.css';

interface richTextTypes {
  richTextData: {
    rich_text: string;
    title?: string;
    id?: number;
  };
  variant?: string;
  background?: string;
  usedFor?: string;
  position?: string;
}

export default function RichText({
  richTextData,
  variant,
  background,
  usedFor = '',
  position = 'left',
}: richTextTypes) {
  return (
    <>
      {variant === 'allServicesPage' ? (
        <Container
          fluid
          className={classNames(
            styles.container,
            styles.containerForAllServicesPage,
          )}
        >
          <Heading
            headingType="h1"
            title={richTextData?.title}
            position="left"
          />
          <div
            dangerouslySetInnerHTML={{
              __html: richTextData.rich_text,
            }}
          />
        </Container>
      ) : variant === 'caseStudy' ? (
        usedFor === 'challenges' ? (
          <Container
            fluid
            className={classNames(
              styles.container,
              styles.containerForCaseStudy,
            )}
            style={{
              background: background === 'black' && variables.colorBlack,
              color: background === 'black' && variables.colorWhite,
            }}
          >
            <Heading
              headingType="h4"
              title={richTextData?.title}
              position="left"
            />
            <div
              className={classNames(
                styles.caseStudy_desc,
                styles.challenges_desc,
              )}
              dangerouslySetInnerHTML={{
                __html: richTextData.rich_text,
              }}
            />
          </Container>
        ) : usedFor === 'results' || usedFor === 'solutions' ? (
          <Container
            fluid
            className={classNames(
              styles.container,
              styles.containerForCaseStudy,
            )}
            style={{
              background: background === 'black' && variables.colorBlack,
              color: background === 'black' && variables.colorWhite,
            }}
          >
            <Heading
              headingType="h4"
              title={richTextData?.title}
              position="left"
            />
            <div
              className={classNames(
                styles.caseStudy_desc,
                styles.resultsAndSolutions_desc,
                background === 'gray' && styles.grayBackground,
              )}
              dangerouslySetInnerHTML={{
                __html: richTextData.rich_text,
              }}
            />
          </Container>
        ) : (
          <Container
            fluid
            className={classNames(
              styles.container,
              styles.containerForCaseStudy,
            )}
            style={{
              background: background === 'black' && variables.colorBlack,
              color: background === 'black' && variables.colorWhite,
            }}
          >
            <Heading
              headingType="h4"
              title={richTextData?.title}
              position={position === 'center' ? 'center' : 'left'}
            />
            <div
              dangerouslySetInnerHTML={{
                __html: richTextData.rich_text,
              }}
            />
          </Container>
        )
      ) : variant === 'aboutUs' ? (
        <Container
          fluid
          className={classNames(
            styles.container,
            styles.containerForAboutUsPage,
          )}
        >
          <div
            dangerouslySetInnerHTML={{
              __html: richTextData.rich_text,
            }}
          />
        </Container>
      ) : (
        <Container fluid className={styles.container}>
          <div
            dangerouslySetInnerHTML={{
              __html: richTextData.rich_text,
            }}
          />
        </Container>
      )}
    </>
  );
}
