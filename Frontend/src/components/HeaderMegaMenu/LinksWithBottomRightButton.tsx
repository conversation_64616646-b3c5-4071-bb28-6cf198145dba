import React from 'react';
import { Col, Row } from 'react-bootstrap';
import Button from '@components/Button';
import styles from '@components/Header/Header.module.css';
import DownArrowIcon from '@components/Icons/DownArrowIcon';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import breakpoints from '@styles/breakpoints.module.css';

import MenuLink from './MenuLink';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function LinksWithBottomRightButton({
  links,
  button,
  onClick,
}: any) {
  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-lg']})`,
  });
  return (
    <>
      <div className={styles.megaMenuContent}>
        <div className={styles.menuWrapper}>
          <Row>
            {links?.map(link => (
              <Col
                className={classNames(
                  'col-sm-12 col-md-3 col-lg-3 col-xl-3',
                  styles.flexDirectionColumn,
                )}
                key={link?.id}
              >
                <MenuLink
                  linkTitle={link?.title}
                  href={link?.link}
                  onClick={onClick}
                />
              </Col>
            ))}
          </Row>
          <Row className={isTablet && styles.hide}>
            <Col className="col-md-3" />
            <Col className="col-md-3" />
            <Col className="col-md-3" />
            <Col className="col-md-3">
              {button && (
                <div className={styles.brandsButton}>
                  <Button
                    type="button"
                    label={button?.title}
                    rightIcon={<DownArrowIcon />}
                    onClick={onClick}
                  />
                </div>
              )}
            </Col>
          </Row>
        </div>
      </div>
      <div className={styles.bottom_border}></div>
    </>
  );
}
