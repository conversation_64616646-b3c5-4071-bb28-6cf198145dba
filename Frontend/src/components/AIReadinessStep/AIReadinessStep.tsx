'use client';
import React, { useState, useEffect } from 'react';
import styles from './AIReadinessStep.module.css';

const steps = [
  'Strategy & Leadership',
  'Data Readiness & Infrastructure',
  'Talent & Skills',
  'Execution & Monitoring',
  'Impact Evaluation',
];

const AIReadinessStep = ({ visibleCount, onStepClick }) => {
  const [selectedStep, setSelectedStep] = useState(1);

  useEffect(() => {
    if (selectedStep < visibleCount + 1) {
      setSelectedStep(visibleCount + 1);
    }
  }, [visibleCount, selectedStep]);

  return (
    <div className={styles.container}>
      {steps.map((label, index) => {
        const stepNumber = index + 1;
        const isHighlighted = stepNumber <= visibleCount + 1;
        const isSelected = stepNumber === selectedStep;

        return (
          <div
            key={index}
            className={styles.stepWrapper}
            onClick={() => {
              if (stepNumber <= visibleCount + 1) {
                setSelectedStep(stepNumber);
                onStepClick(stepNumber); // ✅ notify parent
              }
            }}
          >
            <div
              className={`
                ${styles.circle}
                ${isHighlighted ? styles.active_circle : ''}
                ${isSelected ? styles.selected_circle : ''}
              `}
            >
              {stepNumber}
            </div>
            <p
              className={`
                ${styles.label}
                ${isHighlighted ? styles.active_text : ''}
                ${isSelected ? styles.selected_text : ''}
              `}
            >
              {label}
            </p>
            {index < steps.length - 1 && (
              <div
                className={`${styles.line} ${
                  stepNumber < visibleCount + 1 ? styles.active : ''
                }`}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};
export default AIReadinessStep;
