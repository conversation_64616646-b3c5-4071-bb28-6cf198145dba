'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import styles from './NewsAndEvents.module.css';
import Heading from '@components/Heading';
import Link from 'next/link';

export default function NewsAndEvents({ newsEventsData }) {
  return (
    <Container fluid className={styles.main_container}>
      <div className={styles.inner_container}>
        <div className={styles.news_container}>
          <Heading title="News" headingType="h2" className={styles.title} />
          <div className={styles.news_box_container}>
            {newsEventsData?.news?.data.map((newsData, index) => {
              return (
                <Link
                  href={newsData?.attributes?.slug}
                  className={styles.news_box}
                  target="_blank"
                  prefetch={false}
                >
                  <Heading
                    title={newsData?.attributes?.title}
                    headingType="h3"
                    className={styles.box_title}
                  />
                  <div className={styles.date}>
                    {newsData?.attributes?.date}
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
        <div className={styles.news_container}>
          <Heading title="Events" headingType="h2" className={styles.title} />
          <div className={styles.news_box_container}>
            {newsEventsData?.event_main_pages?.data.map((eventsData, index) => {
              return (
                <Link
                  href={`/events/${eventsData?.attributes?.slug}`}
                  className={styles.news_box}
                  prefetch={false}
                >
                  <Heading
                    title={eventsData?.attributes?.hero_section?.hero_title}
                    headingType="h3"
                    className={styles.box_title}
                  />
                  <div className={styles.date}>
                    {eventsData?.attributes?.hero_section?.date_value}
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
        <div className={styles.events_container}></div>
      </div>
    </Container>
  );
}
