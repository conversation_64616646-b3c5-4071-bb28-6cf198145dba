@value variables: "@styles/variables.module.css";
@value colorWhite, colorBlack, gray500 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-xl-1440 from breakpoints;

.sectionWrapper {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.headerContent {
  padding: 5rem 9.375rem 0 9.375rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem 0 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 2.5rem 2rem 0 2rem;
    text-align: center;
    flex-direction: column;
  }

  @media (max-width: breakpoint-sm) {
    padding: 2.5rem 1rem 0 1rem;
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.title h2 {
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
}

.description {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.carouselArrowButtons {
  display: flex;

  @media (max-width: breakpoint-md) {
    display: none;
  }
}

.embla {
  max-width: 100%;
  margin: auto;
  padding-bottom: 5rem;
  --slide-height: 586px;
  --slide-spacing: 30px;
  --slide-size: 520px;
  position: relative;
 
  @media (max-width: breakpoint-md) {
    padding-bottom: 2.5rem;
    max-width: 520px;
    --slide-size: 89.5%;
  }

  @media (max-width: breakpoint-sm) {
    max-width: 100%;
    --slide-size: 92%;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
}

.embla__slide:first-child {
  margin-left: 9.375rem;

  @media (max-width: breakpoint-xl-1440) {
    margin-left: 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    margin-left: 2rem;
  }

  @media (max-width: breakpoint-sm) {
    margin-left: 1rem;
  }
}

.embla__slide:last-child {
  margin-right: 9.375rem;

  @media (max-width: breakpoint-xl-1440) {
    margin-right: 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    margin-right: 2rem;
  }

  @media (max-width: breakpoint-sm) {
    margin-right: 1rem;
  }
}

.embla__slide__number {
  display: flex;
  flex-direction: column;
  gap: 30px;
  background: colorBlack;
  color: colorWhite;
  border-radius: 6px;
  height: var(--slide-height);
}

.boxTitle > h3 {
  padding: 24px 24px 0 24px;
  font-size: 32px;
  font-weight: 600;
  line-height: 42.24px;
  letter-spacing: -0.01em;
}

.boxDescription {
  padding: 0 24px;
  font-size: 20px;
  font-weight: 400;
  line-height: 32px;
}

.imageWrapper {
  width: 100%;
  height: 100%;
  box-shadow: inset 800px 800px 800px -700px colorBlack ;
  border-radius: 6px;
  background-image: var(--bg-image);
  background-size: cover;
  background-position: center;
}

.embla__dotsContainer {
  display: none;

  @media (max-width: breakpoint-md) {
    display: block;
  }
}