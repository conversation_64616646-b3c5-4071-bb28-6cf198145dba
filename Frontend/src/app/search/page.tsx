import ContactUsForm from '@components/ContactUsForm';
import InstantSearchWrapper from '@components/InstantSearchWrapper';
import seoSchema from '@utils/seoSchema';
import RichResults from '@components/RichResults';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function fetchSearchPageData() {
  const query = `populate=initial_search_results.blogs,initial_search_results.case_studies,initial_search_results.l2_service_pages,initial_search_results.l3_service_pages,initial_search_results.industries,initial_search_results.partners,initial_search_results.custom_data_for_initial_search_results&populate=form.formFields&populate=form.button,seo.schema`;
  return await fetchFromStrapi('search', query);
}

export async function getFormData() {
  return await fetchFromStrapi(
    'form',
    'populate=form.formFields&populate=form.button',
  );
}

export async function generateMetadata() {
  const query = `populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema`;
  const seoFetchedData = await fetchFromStrapi('search', query);

  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function SearchPage() {
  const searchPageData = await fetchSearchPageData();
  const formData = await getFormData();

  return (
    <>
      {searchPageData?.data?.attributes?.seo && (
        <RichResults data={searchPageData?.data?.attributes?.seo} />
      )}
      <InstantSearchWrapper
        searchPageInitialData={searchPageData?.data?.attributes}
      />
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="Search"
        />
      )}
    </>
  );
}
