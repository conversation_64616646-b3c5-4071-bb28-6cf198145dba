# ------------------------
# S3 Bucket
# ------------------------
resource "aws_s3_bucket" "static_site" {
  bucket        = "mtl-site-${var.environment}-environment"
  force_destroy = true
  tags = merge(var.common_tags, {
    Name        = "mtl-site-${var.environment}-environment",
    Description = "Static site bucket for Maruti Tech - ${var.environment}"
  })
}

# Enable S3 Versioning
resource "aws_s3_bucket_versioning" "this" {
  bucket = aws_s3_bucket.static_site.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Enable Static Website Hosting
resource "aws_s3_bucket_website_configuration" "this" {
  bucket = aws_s3_bucket.static_site.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "404.html"
  }
}

# Blocked All Public Access
resource "aws_s3_bucket_public_access_block" "this" {
  bucket = aws_s3_bucket.static_site.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 Bucket Policy
resource "aws_s3_bucket_policy" "allow_cloudfront" {
  bucket = aws_s3_bucket.static_site.id
  policy = data.aws_iam_policy_document.allow_cloudfront.json
}

# ------------------------
# S3 Bucket Policy (OAC-based, using SourceArn)
# ------------------------
data "aws_iam_policy_document" "allow_cloudfront" {
  version = "2008-10-17"
  statement {
    sid    = "AllowCloudFrontServicePrincipalReadOnly"
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudfront.amazonaws.com"]
    }
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.static_site.arn}/*"]
    condition {
      test     = "StringEquals"
      variable = "AWS:SourceArn"
      values   = ["${aws_cloudfront_distribution.mtl-cloudfront.arn}"]
    }
  }
}

# --------------------------
# CORS Configuration
# --------------------------
resource "aws_s3_bucket_cors_configuration" "static" {
  bucket = aws_s3_bucket.static_site.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET"]
    allowed_origins = ["*"]
    expose_headers  = []
    max_age_seconds = 3000
  }
}

# --------------------------
# --------------------------
#  maruti-site-cdn bucket for Image Storage
# --------------------------
# --------------------------

# resource "aws_s3_bucket" "maruti_site_cdn" {
#   bucket        = "maruti-site-cdn"
#   force_destroy = true
#   tags = {
#     Name        = "Maruti Site CDN Bucket",
#     Description = "CDN S3 bucket for static image assets",
#     ManagedBy   = "Terraform"
#   }
# }

# Enable S3 Versioning
# resource "aws_s3_bucket_versioning" "versioning_maruti_site_cdn" {
#   bucket = aws_s3_bucket.maruti_site_cdn.id

#   versioning_configuration {
#     status = "Enabled"
#   }
# }

# Allow ACLs and Disable Object Ownership Enforcement
# resource "aws_s3_bucket_ownership_controls" "ownership_controls" {
#   bucket = aws_s3_bucket.maruti_site_cdn.id

#   rule {
#     object_ownership = "ObjectWriter" # Enables ACLs
#   }
# }

# OFF Block Public Access (to allow ACLs to work)
# resource "aws_s3_bucket_public_access_block" "block_public_access_maruti_site_cdn" {
#   bucket = aws_s3_bucket.maruti_site_cdn.id

#   block_public_acls       = false
#   block_public_policy     = false
#   ignore_public_acls      = false
#   restrict_public_buckets = false
# }

# S3 Bucket Policy
# resource "aws_s3_bucket_policy" "allow_cloudfront_maruti_site_cdn" {
#   bucket = aws_s3_bucket.maruti_site_cdn.id
#   policy = data.aws_iam_policy_document.allow_cloudfront_maruti_site_cdn.json
# }

# ------------------------
# S3 Bucket Policy (OAC-based, using SourceArn)
# ------------------------
# data "aws_iam_policy_document" "allow_cloudfront_maruti_site_cdn" {
#   version = "2008-10-17"

#   statement {
#     sid    = "AllowCloudFrontServicePrincipal"
#     effect = "Allow"
#     principals {
#       type        = "Service"
#       identifiers = ["cloudfront.amazonaws.com"]
#     }
#     actions   = ["s3:GetObject"]
#     resources = ["${aws_s3_bucket.maruti_site_cdn.arn}/*"]
#     condition {
#       test     = "StringEquals"
#       variable = "AWS:SourceArn"
#       values   = ["${aws_cloudfront_distribution.cdn.arn}"]
#     }
#   }

#   statement {
#     sid    = "AllowCICDUserToAccessBucket"
#     effect = "Allow"
#     principals {
#       type        = "AWS"
#       identifiers = ["arn:aws:iam::851725323009:user/marutisite.CICDuser"]
#     }
#     actions = [
#       "s3:PutObject",
#       "s3:GetObject",
#       "s3:DeleteObject",
#       "s3:ListBucket"
#     ]
#     resources = [
#       aws_s3_bucket.maruti_site_cdn.arn,
#       "${aws_s3_bucket.maruti_site_cdn.arn}/*"
#     ]
#   }
# }