output "bucket_arn" {
  value = aws_s3_bucket.static_site.arn
}

output "bucket_regional_domain" {
  value = aws_s3_bucket.static_site.bucket_regional_domain_name
}

output "cloudfront_url" {
  description = "The CloudFront distribution URL"
  value       = "https://${aws_cloudfront_distribution.mtl-cloudfront.domain_name}"
}

# The base URL of the deployed REST API
output "api_invoke_url" {
  value       = "${aws_api_gateway_rest_api.maruti_site_api.execution_arn}/"
  description = "Invoke URL for the Maruti REST API"
}

# The full URL for /contact-us POST endpoint
output "contact_us_endpoint" {
  value       = "${aws_api_gateway_rest_api.maruti_site_api.execution_arn}/${aws_api_gateway_stage.maruti_api_stage.stage_name}/contact-us"
  description = "POST endpoint for /contact-us form"
}

# The full URL for /ai-readiness POST endpoint
output "ai_readiness_endpoint" {
  value       = "${aws_api_gateway_rest_api.maruti_site_api.execution_arn}/${aws_api_gateway_stage.maruti_api_stage.stage_name}/ai-readiness"
  description = "POST endpoint for /ai-readiness form"
}

# Output CloudFront DNS Target
output "api_custom_domain_cloudfront" {
  value = aws_api_gateway_domain_name.custom_domain.cloudfront_domain_name
}