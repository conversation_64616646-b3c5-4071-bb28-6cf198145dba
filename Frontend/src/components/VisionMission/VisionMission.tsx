'use client';
import React from 'react';
import { Container } from 'react-bootstrap';
import Heading from '@components/Heading';
import styles from './VisonMission.module.css';
import ImageWithBlurPreview from '@components/ImageWithBlurPreview';

export default function VisionMission({ visionMissionData }) {
  return (
    <>
      <Container fluid className={styles.main_container}>
        {visionMissionData?.map((serviceData, index) => {
          if (index % 2 === 0) {
            return (
              <div className={styles.vision_container} key={index}>
                <div className={styles.image_container}>
                  <ImageWithBlurPreview
                    data={serviceData?.image?.data?.attributes}
                    width={720}
                    height={500}
                    mainClass={styles.image}
                    priority={true}
                    quality={95}
                    loading="eager"
                  />
                </div>
                <div className={styles.box_container}>
                  <Heading
                    headingType="h2"
                    title={serviceData?.title}
                    className={styles.title}
                  />
                  <div
                    className={styles.richText}
                    dangerouslySetInnerHTML={{
                      __html: serviceData?.description,
                    }}
                  ></div>
                </div>
              </div>
            );
          } else {
            return (
              <div className={styles.mission_container} key={index}>
                <div className={styles.image_container}>
                  <ImageWithBlurPreview
                    data={serviceData?.image?.data?.attributes}
                    width={720}
                    height={500}
                    mainClass={styles.image}
                    priority={true}
                    quality={95}
                    loading="eager"
                  />
                </div>
                <div className={styles.box_container}>
                  <Heading
                    headingType="h2"
                    title={serviceData?.title}
                    className={styles.title}
                  />
                  <div
                    className={styles.richText}
                    dangerouslySetInnerHTML={{
                      __html: serviceData?.description,
                    }}
                  ></div>
                </div>
              </div>
            );
          }
        })}
      </Container>
    </>
  );
}
