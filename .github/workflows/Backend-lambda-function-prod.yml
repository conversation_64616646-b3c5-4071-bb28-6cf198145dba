name: Deploy Backend Lambda Function - Prod

on:
  push:
    branches:
      - main
    paths:
      - Backend/**
      - .github/workflows/Backend-lambda-function-prod.yml
  workflow_dispatch:  # Allow manual trigger from GitHub UI

jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      FUNCTION_NAME: mtl_site_function-prod  # Lambda function name
      AWS_REGION: ap-south-1                 # AWS Region

    steps:

    # Determine display name for the author
    - name: Check if author needs to be replaced
      id: check_author
      run: |
        if [ "${{ github.actor }}" = "24072012" ]; then
          echo "author=Strapi" >> $GITHUB_OUTPUT
        else
          echo "author=${{ github.actor }}" >> $GITHUB_OUTPUT
        fi
      shell: bash

    # Notify Slack that deployment has started
    - name: Notify Deployment startup to Slack
      uses: slackapi/slack-github-action@v1.23.0
      with:
        channel-id: 'C05EU6F4EG6'
        payload: |
          {
            "text":  "_Event: Deployment Started_ :arrows_counterclockwise: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda Function \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
            "blocks": [
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "_Event: Deployment Started_ :arrows_counterclockwise: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda Function \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            ]
          }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

    - name: Checkout code
      uses: actions/checkout@v4

    # Install Node.js dependencies for Lambda function
    - name: Install dependencies
      run: |
        cd Backend
        npm install
    
    # Configure AWS credentials for ap-south-1 region
    - name: Set up AWS CLI
      uses: aws-actions/configure-aws-credentials@v3
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    # Zip the Lambda function code
    - name: Zip Lambda function
      run: |
        cd Backend
        zip -r ../backend_lambda_function_prod.zip .

    # Deploy the zipped code to AWS Lambda
    - name: Deploy to AWS Lambda
      run: |
        aws lambda update-function-code \
          --function-name $FUNCTION_NAME \
          --zip-file fileb://backend_lambda_function_prod.zip

    # Notify Slack when the deployment completes successfully
    - name: Notify Deployment completion to Slack
      uses: slackapi/slack-github-action@v1.23.0
      with:
        channel-id: 'C05EU6F4EG6'
        payload: |
          {
            "text":  "_Event: Deployment Completed_ :rocket: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda Function \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
            "blocks": [
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "_Event: Deployment Completed_ :rocket: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda Function \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            ]
          }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

    # Notify Slack if any previous step in the workflow fails
    - name: Notify Deployment failure to Slack
      if: failure()
      uses: slackapi/slack-github-action@v1.23.0
      with:
        channel-id: 'C05EU6F4EG6'
        payload: |
          {
            "text":  "_Event: Deployment Failed_ :rotating_light: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda Function \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
            "blocks": [
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "_Event: Deployment Failed_ :rotating_light: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Backend Lambda Function \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            ]
          }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}