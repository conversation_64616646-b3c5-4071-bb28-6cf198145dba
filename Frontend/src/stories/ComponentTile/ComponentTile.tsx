import type { ReactNode } from 'react';
import React from 'react';
import typographyStyles from '@styles/typography.module.css';

import styles from './ComponentTile.module.css';

function ComponentTile({
  label,
  children,
}: {
  label?: string;
  children: ReactNode;
}) {
  return (
    <div className={styles.componentTile}>
      <div className={styles.container}>
        <div className={styles.childrenWrapper}>{children}</div>

        <h3 className={[styles.label, typographyStyles.caption].join(' ')}>
          {label}
        </h3>
      </div>
    </div>
  );
}

export default ComponentTile;
