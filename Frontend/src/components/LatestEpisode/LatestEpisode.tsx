'use client';
import styles from './LatestEpisode.module.css';
import Heading from '@components/Heading';
import YTVideo from '@components/YTVideo';
import classNames from '@utils/classNames';
import { Container } from 'react-bootstrap';

export default function LatestEpisode({
  data,
  playButtonUrl,
  fromCloudConsulting = false,
}) {
  const fromCloud = fromCloudConsulting ? styles.fromCloud : '';
  return (
    <Container fluid className={classNames(styles.container, fromCloud)}>
      <div className={styles.content}>
        <Heading
          headingType="h2"
          title={data?.title}
          className={classNames(styles.title, fromCloud)}
        />
        {data?.subtitle && (
          <div
            className={classNames(styles.subtitle, fromCloud)}
            dangerouslySetInnerHTML={{ __html: data?.subtitle }}
          ></div>
        )}
      </div>
      <div className={styles.videoContainer}>
        <YTVideo
          embedLink={data?.youtube_video_embed_link}
          playButtonUrl={playButtonUrl}
          thumbnailUrl={data?.video_thumbnail_image?.data?.attributes?.url}
        />
      </div>
    </Container>
  );
}
