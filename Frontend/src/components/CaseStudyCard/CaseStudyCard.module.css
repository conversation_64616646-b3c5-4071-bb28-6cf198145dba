@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-lg, breakpoint-xl-2000, breakpoint-xl-1800, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px, breakpoint-sm from breakpoints;

.main_container {
  min-height: 756px;
  background: colorBlack;
  padding: 80px 0;
  display: flex;
  justify-content: center;
  overflow: hidden;

  @media screen and (max-width: 1285px) {
    padding: 80px 32px;
    min-height: 808px;
  }

  @media screen and (max-width: breakpoint-sm-450) {
    padding: 40px 32px;
  }
}

.main_container_black {
  background: colorWhite;
}

.inner_container {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.heading_box {
  display: flex;
  justify-content: space-between;
  max-width: 1192px;

  @media screen and (max-width: breakpoint-sm-550) {
    flex-direction: column;
    gap: 24px;
    align-items: center;

    .title {
      font-size: 28px;
      line-height: 138%;
    }
  }
}

.title>h2 {
  color: colorWhite;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 56px;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 138%;
  }
}

.title_black>h2 {
  color: colorBlack;
}

.view_all_link {
  display: flex;
  align-items: center;
  color: colorWhite;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 25px;
  z-index: 2;
}

.view_all_link_black {
  color: colorBlack;
  z-index: 2;
}

.card_box {
  display: flex;
  justify-content: center;
  gap: 20px;
  position: relative;
  max-width: 1192px;
}

.card {
  position: relative;
  display: flex;
  height: 500px;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  overflow: hidden;
  cursor: pointer;
  border-radius: 6px;
  transition: transform 0.3s ease-in-out;
}

.overlay {
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 24px;
  z-index: 1;
}

.badge {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: colorWhite;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.7);
  padding: 2px 6px;
  width: fit-content;
}

.box_title h3 {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 148%;
  color: colorWhite;
}

.arrow_button {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  z-index: 10;
  bottom: -12%;
  right: 24px;
  transition: transform 0.5s ease;
}

.card:hover {
  .arrow_button {
    transform: translateY(-150%);
  }

  @media screen and (min-width: breakpoint-lg) {
    .image {
      filter: blur(8px);
    }
  }

}

.card:not(:hover) {
  .arrow_button {
    transform: translateY(0);
  }
}

@media screen and (max-width: breakpoint-lg-991px) {
  .card:hover .arrow_button {
    transform: none;
  }

  .arrow_button {
    transition: none;
    bottom: 5%;
    right: 30px;
  }
}

.embla {
  max-width: 68rem;

  /* --slide-size: 12%; */
  @media screen and (max-width: 1222px) {
    max-width: 55rem;
  }

  @media screen and (max-width: 1047px) {
    max-width: 45rem;
  }

  @media screen and (max-width: 869px) {
    max-width: 35rem;
  }

  @media screen and (max-width: 725px) {
    max-width: 25rem;
  }

  @media screen and (max-width: 550px) {
    max-width: 18rem;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  display: flex;
}

.embla__slide {
  margin-left: 20px;
}

.carousel {
  padding: 0;
  position: relative;
  display: flex;
  justify-content: center;

  @media screen and (max-width: 400px) {
    padding: 0 10px;
  }

  @media screen and (min-width: 401px) and (max-width: breakpoint-sm-450) {
    padding: 0 38px !important;
  }

  .top_left {
    top: -68px;
    left: -32px;
  }

  .bottom_right {
    bottom: 11px;
    right: -25px;
  }
}

.gradient_image_wrapper {
  position: absolute;
  width: 240px;
  height: 194px;
  z-index: 0;
}

.top_left {
  top: -68px;
  left: -97px;
}

.bottom_right {
  bottom: -68px;
  right: -97px;
}

.reversed_image {
  transform: scaleX(-1) scaleY(-1);
  z-index: -1;
}

.expertise_delivered {
  display: flex;
  gap: 10px;
  max-width: fit-content;
  flex-wrap: wrap;
}

.smallbox {
  display: flex;
  background-color: #ffffffb3;
  justify-content: center;
  width: max-content;
  padding: 2px 6px;
  border-radius: 3px;
  gap: 2px;

  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  width: auto;

  .pipe {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
  }
}

.smallbox:last-child {
  .pipe {
    display: none;
  }
}