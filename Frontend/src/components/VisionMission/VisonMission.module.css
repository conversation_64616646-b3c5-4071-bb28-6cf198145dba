@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, brandColorThree, fifteenSpace, grayBorder from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-xl-2000, breakpoint-sm, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.main_container {
  padding: 0;
}

.vision_container {
  display: flex;

  @media screen and (max-width: 768px) {
    flex-direction: column;
  }
}

.mission_container {
  display: flex;
  flex-direction: row-reverse;

  @media screen and (max-width: 768px) {
    flex-direction: column;
  }
}

.box_container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px;
  gap: 8px;
  background-color: colorBlack;
}

.title>h2 {
  color: colorWhite;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
  }
}

.richText {
  color: colorWhite;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.image_container {
  overflow: hidden;
}

.image_container,
.box_container {
  width: 50%;

  @media screen and (max-width: 768px) {
    width: 100%;
  }
}

.image {
  width: 100%;
  transition: transform 0.5s ease;

  @media screen and (max-width: breakpoint-sm-550) {
    height: 320px;
  }
}

.image_container:hover .image {
  transform: scale(1.1);
}