@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-lg, breakpoint-md, breakpoint-sm, breakpoint-sm-427 from breakpoints;

.container * {
  font-family: var(--font-poppins) !important;
}

.container {
  margin: 0;
  padding: 5rem 9.375rem;
  background-color: gray300;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 5rem 2rem;
  }

  @media (max-width: breakpoint-sm) {
    padding: 5rem 1rem;
  }
}

.containerForAllServicesPage {
  background-color: gray200;
}

.containerForCaseStudy {
  /* padding: 5rem 7.75rem; */
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background-color: colorWhite;
  color: colorBlack;
}

.containerForAboutUsPage {
  @media (max-width: breakpoint-md) {
    padding: 2.5rem 2rem;
  }
}

.caseStudy_desc ul {
  display: flex;
  flex-direction: column;
  gap: 8px;
  list-style: none;
}



.caseStudy_desc ul>li::before {
  display: inline-block;
  width: 24px;
  height: 24px;
  position: relative;
  top: 5px;
  margin-right: 5px;
}

.caseStudy_desc ul>li>span {
  position: relative;
  top: -5px;
}

.challenges_desc ul>li::before {
  content: url('https://cdn.marutitech.com/help_circle_1_12b48b8eb2.svg');
}

.resultsAndSolutions_desc ul>li::before {
  content: url('https://cdn.marutitech.com/check_circle_21200b9036.svg');
}

.grayBackground ul>li::before {
  content: url('https://cdn.marutitech.com/check_circle_6f6ba688fe.svg') !important;
}