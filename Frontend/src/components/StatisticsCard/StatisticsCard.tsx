'use client';

import { Container } from 'react-bootstrap';
import CountUp from 'react-countup';

import styles from './StatisticsCard.module.css';
import Heading from '@components/Heading';

function TopGradient() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="36"
      height="4"
      viewBox="0 0 36 4"
      fill="none"
    >
      <path
        d="M2 2H34"
        stroke="url(#paint0_linear_3647_3464)"
        strokeWidth="4"
        strokeLinecap="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_3647_3464"
          x1="2"
          y1="1.99997"
          x2="10.3617"
          y2="16.5679"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEBE10" />
          <stop offset="0.305582" stopColor="#F47A37" />
          <stop offset="0.53474" stopColor="#F05443" />
          <stop offset="0.757546" stopColor="#D91A5F" />
          <stop offset="1" stopColor="#B41F5E" />
        </linearGradient>
      </defs>
    </svg>
  );
}

type StatisticsCardsDataTypes = {
  id: number;
  statistics: number;
  suffix: string;
  decimalValue: boolean;
  numbersOfDigitAfterDecimal: number;
  description: string;
  box_title: string;
};

export default function StatisticsCard({ statisticsCardData }) {
  return (
    <Container fluid className={styles.statisticsCardContainer}>
      <div className={styles.componentTitleWrapper}>
        <Heading
          title={statisticsCardData?.Title}
          headingType="h2"
          className={styles.componentTitle}
        />
      </div>
      <div className={styles.boxWrapper_container}>
        <div className={styles.boxWrapper}>
          {statisticsCardData?.statisticsCards?.map(
            (item: StatisticsCardsDataTypes) => {
              const DecimalValueValidation =
                item?.decimalValue === true && item?.numbersOfDigitAfterDecimal
                  ? item?.numbersOfDigitAfterDecimal
                  : 0;
              return (
                <div key={item?.id} className={styles.wrapper}>
                  <div className={styles.box}>
                    <div className={styles.topSection}>
                      <TopGradient />
                      <CountUp
                        className={styles.statistics}
                        end={item?.statistics}
                        suffix={item?.suffix ? item.suffix : ''}
                        decimals={DecimalValueValidation}
                        enableScrollSpy={true}
                        scrollSpyOnce={true}
                      />
                    </div>
                    <Heading
                      headingType="h3"
                      title={item.box_title}
                      className={styles.box_title}
                    />
                    <p className={styles.statisticsDescription}>
                      {item?.description}
                    </p>
                  </div>
                  <div className={styles.gradientWrapper}></div>
                </div>
              );
            },
          )}
        </div>
      </div>
    </Container>
  );
}
