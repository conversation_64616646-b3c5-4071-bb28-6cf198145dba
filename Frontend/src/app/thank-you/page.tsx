import ThankYou from '@components/ThankYou';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import seoSchema from '@utils/seoSchema';
import RichResults from '@components/RichResults'; // Ensure this is the correct path

async function fetchThankYouPageData() {
  const query = `populate=thank_you.button,seo.schema`;
  return await fetchFromStrapi('thank-you', query);
}

export async function generateMetadata({}) {
  const query = `populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema`;
  const seoFetchedData = await fetchFromStrapi('thank-you', query);

  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}
export default async function ThankYouPage() {
  const thankYouPageData = await fetchThankYouPageData();

  return (
    <>
      {thankYouPageData?.data?.attributes?.seo && (
        <RichResults data={thankYouPageData?.data?.attributes?.seo} />
      )}
      {thankYouPageData?.data?.attributes?.thank_you && (
        <ThankYou
          dataThankYou={thankYouPageData?.data?.attributes?.thank_you}
        />
      )}
    </>
  );
}
