@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md from breakpoints;

.main_container {
  display: flex;
  justify-content: center;
  padding: 80px 124px;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }
}

.inner_container {
  display: flex;
  flex-direction: column;
  gap: 40px;
  max-width: 1192px;
}

.news_container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.news_box_container {
  display: flex;
  gap: 20px;

  @media screen and (max-width: breakpoint-md) {
    flex-direction: column;
  }
}

.news_box {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 16px 16px 16px 0;
  width: 100%;
  text-decoration: none;
}

.title > h2 {
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
  letter-spacing: -0.8px;
}

.box_title > h3 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
  letter-spacing: 0.48px;
}
