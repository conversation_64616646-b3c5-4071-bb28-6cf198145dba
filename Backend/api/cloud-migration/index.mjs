/**
 * @fileoverview Cloud Migration Cost Calculator form submission handler
 * This AWS Lambda function handles cloud migration cost calculator form submissions by:
 * 1. Parsing cloud migration form data from the event
 * 2. Sending data to HubSpot CRM with migration requirements and cost estimates
 * 3. Sending detailed cost calculation report email via SendGrid
 * 4. Sending notification to Slack with migration summary
 *
 * Configuration is loaded from AWS SSM Parameter Store with fallback to environment variables.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import sendDataToHubspot from "../../common/sendDataToHubSpot.mjs";
import sendDataToSendGrid from "../../common/sendDataToSendGrid.mjs";
import currentTimestamp from "../../common/currentTimestamp.mjs";
import sendToSlack from "../../common/sendDataToSlack.mjs";
import { getConfigValue } from "../../common/ssmConfig.mjs";

/**
 * AWS Lambda handler for cloud migration cost calculator form submissions
 *
 * @async
 * @function handler
 * @param {Object} event - AWS Lambda event object
 * @param {string} event.body - JSON string containing cloud migration form data
 * @param {Object} event.headers - HTTP headers from the request
 * @param {Object} event.requestContext - AWS Lambda request context
 * @returns {Promise<Object>} Lambda response object with statusCode and body
 *
 * @example
 * // Example event.body structure (matches exact form data keys):
 * {
 *   "firstname": "John",
 *   "lastname": "Smith",
 *   "email": "<EMAIL>",
 *   "company": "Tech Solutions Inc",
 *   "phone": "+**********",
 *   "country": "USA",
 *   "minimum_cost": "50000",
 *   "maximum_migration_cost": "100000",
 *   "which_elements_are_you_planning_to_migrate_to_the_cloud_": "Applications and databases",
 *   "approximately_how_many_servers_do_you_intend_to_migrate_": "10-50",
 *   "what_is_the_type_of_data_migration_you_intend_to_do_": "Full migration",
 *   "what_is_your_current_it_infrastructure_setup_": "On-premises",
 *   "what_is_the_total_capacity_of_your_servers_": "500GB",
 *   "what_is_the_current_monthly_infrastructure_cost_of_your_current_setup_": "$5000",
 *   "what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud____multiple_choice_allowed_": ["Cost reduction", "Scalability"],
 *   "what_type_of_workloads_do_you_run___multiple_choice_allowed_": ["Web Applications", "Databases"],
 *   "what_is_the_average_cpu_and_memory_usage_of_your_workloads_": "Medium",
 *   "do_you_require_high_availability_or_disaster_recovery_for_critical_applications_": "Yes",
 *   "which_cloud_provider_s__are_you_considering_": "AWS",
 *   "do_you_plan_to_use_reserved_instances__spot_instances__or_pay_as_you_go_pricing_models_": "Reserved instances",
 *   "which_cloud_environments_are_you_planning_to_deploy___multiple_choice_allowed_": ["Production", "Development"],
 *   "do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_m": "GDPR",
 *   "what_migration_strategy_do_you_prefer_": "Lift and shift",
 *   "do_you_need_auto_scaling_capabilities_for_cost_optimization_": "Yes",
 *   "how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses_": "Monthly",
 *   "consent": true,
 *   "source": "Cloud Migration Cost Calculator",
 *   "source_url": "https://example.com/cloud-migration-calculator",
 *   "utm_campaign": "cloud-migration",
 *   "utm_medium": "organic",
 *   "utm_source": "website",
 *   "referrer": "https://google.com",
 *   "ga_4_userid": "GA1.2.123456789.**********",
 *   "ip_address": "***********"
 * }
 *
 * @example
 * // Success response:
 * {
 *   "statusCode": 200,
 *   "body": "{\"message\":\"Form submitted successfully.\",\"hubspotResponse\":\"CloudMigration form data sent to HubSpot successfully.\"}"
 * }
 */
export const handler = async (event) => {
  try {
    const form_data = JSON.parse(event.body);

    console.log(currentTimestamp());
    console.log("Cloud Migration Form Data Received:", form_data);

    // Map form fields to HubSpot properties - name matches HubSpot fields, value uses original form data keys
    const formFields = [
      // Basic contact information
      { name: "firstname", value: form_data?.firstName ?? "" },
      { name: "lastname", value: form_data?.lastName ?? "" },
      { name: "email", value: form_data?.emailAddress ?? "" },
      { name: "company", value: form_data?.companyName ?? "" },
      { name: "phone", value: form_data?.phoneNumber ?? "" },
      { name: "country", value: form_data?.country ?? "" },

      // Tracking and source information
      { name: "ip_address", value: form_data?.ip_address ?? "" },
      { name: "ga_4_userid", value: form_data?.ga_4_userid ?? "" },
      {
        name: "source",
        value: form_data?.secondary_source ?? "Cloud Migration Cost Calculator",
      },
      { name: "source_url", value: form_data?.url ?? "" },
      { name: "utm_campaign", value: form_data?.utm_campaign ?? "" },
      { name: "utm_source", value: form_data?.utm_source ?? "" },
      { name: "utm_medium", value: form_data?.utm_medium ?? "" },
      { name: "referrer", value: form_data?.referrer ?? "" },
      { name: "consent", value: form_data?.consent ?? "" },

      // Cloud Migration Cost Calculation Results
      {
        name: "minimum_cost",
        value: form_data?.minimum_cost ?? form_data?.lowerRange ?? "",
      },
      {
        name: "maximum_migration_cost",
        value: form_data?.maximum_migration_cost ?? form_data?.upperRange ?? "",
      },
      { name: "total_estimated_cost", value: form_data?.totalCost ?? "" },

      // Cloud Migration Assessment Questions - name uses new HubSpot fields, value uses original form data keys
      {
        name: "which_elements_are_you_planning_to_migrate_to_the_cloud_",
        value:
          form_data?.which_elements_are_you_planning_to_migrate_to_the_cloud ??
          "",
      },
      {
        name: "approximately_how_many_servers_do_you_intend_to_migrate_",
        value:
          form_data?.approximately_how_many_servers_do_you_intend_to_migrate ??
          "",
      },
      {
        name: "what_is_the_type_of_data_migration_you_intend_to_do_",
        value:
          form_data?.what_is_the_type_of_data_migration_you_intend_to_do ?? "",
      },
      {
        name: "what_is_your_current_it_infrastructure_setup_",
        value: form_data?.what_is_your_current_it_infrastructure_setup ?? "",
      },
      {
        name: "what_is_the_total_capacity_of_your_servers_",
        value: form_data?.what_is_the_total_capacity_of_your_servers ?? "",
      },
      {
        name: "what_is_the_current_monthly_infrastructure_cost_of_your_current_setup_",
        value:
          form_data?.what_is_the_current_monthly_infrastructure_cost_of_your_current_setup ??
          "",
      },
      {
        name: "what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud____multiple_choice_allowed_",
        value: Array.isArray(
          form_data?.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud
        )
          ? form_data.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud.join(
              ", "
            )
          : form_data?.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud ??
            "",
      },
      {
        name: "what_type_of_workloads_do_you_run___multiple_choice_allowed_",
        value: Array.isArray(form_data?.what_type_of_workloads_do_you_run)
          ? form_data.what_type_of_workloads_do_you_run.join(", ")
          : form_data?.what_type_of_workloads_do_you_run ?? "",
      },
      {
        name: "what_is_the_average_cpu_and_memory_usage_of_your_workloads_",
        value:
          form_data?.what_is_the_average_cpu_and_memory_usage_of_your_workloads ??
          "",
      },
      {
        name: "do_you_require_high_availability_or_disaster_recovery_for_critical_applications_",
        value:
          form_data?.do_you_require_high_availability_or_disaster_recovery_for_critical_applications ??
          "",
      },
      {
        name: "which_cloud_provider_s__are_you_considering_",
        value: form_data?.which_cloud_provider_s_are_you_considering ?? "",
      },
      {
        name: "do_you_plan_to_use_reserved_instances__spot_instances__or_pay_as_you_go_pricing_models_",
        value:
          form_data?.do_you_plan_to_use_reserved_instances_spot_instances_or_pay_as_you_go_pricing_models ??
          "",
      },
      {
        name: "which_cloud_environments_are_you_planning_to_deploy___multiple_choice_allowed_",
        value: Array.isArray(
          form_data?.which_cloud_environments_are_you_planning_to_deploy
        )
          ? form_data.which_cloud_environments_are_you_planning_to_deploy.join(
              ", "
            )
          : form_data?.which_cloud_environments_are_you_planning_to_deploy ??
            "",
      },
      {
        name: "do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_m",
        value:
          form_data?.do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_meet ??
          "",
      },
      {
        name: "what_migration_strategy_do_you_prefer_",
        value: Array.isArray(form_data?.what_migration_strategy_do_you_prefer)
          ? form_data.what_migration_strategy_do_you_prefer.join(", ")
          : form_data?.what_migration_strategy_do_you_prefer ?? "",
      },
      {
        name: "do_you_need_auto_scaling_capabilities_for_cost_optimization_",
        value:
          form_data?.do_you_need_auto_scaling_capabilities_for_cost_optimization ??
          "",
      },
      {
        name: "how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses_",
        value:
          form_data?.how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses ??
          "",
      },

      // Cost breakdown factors (for internal tracking)
      {
        name: "server_infrastructure_cost",
        value: form_data?.costFactors?.serverCount ?? "",
      },
      {
        name: "data_capacity_cost",
        value: form_data?.costFactors?.dataCapacity ?? "",
      },
      {
        name: "high_availability_cost",
        value: form_data?.costFactors?.highAvailability ?? "",
      },
      {
        name: "environments_cost",
        value: form_data?.costFactors?.environments ?? "",
      },
      {
        name: "compliance_cost",
        value: form_data?.costFactors?.compliance ?? "",
      },
      {
        name: "migration_strategy_cost",
        value: form_data?.costFactors?.migrationStrategy ?? "",
      },
      {
        name: "auto_scaling_cost",
        value: form_data?.costFactors?.autoScaling ?? "",
      },
    ];

    // Get configuration values from SSM
    const [hubspotApiKey, hubspotFormGuid] = await Promise.all([
      getConfigValue("HUBSPOT_API_KEY"),
      getConfigValue("HUBSPOT_CLOUD_MIGRATION_FORM_GUID"),
    ]);

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${hubspotApiKey}`,
      },
    };

    try {
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        hubspotFormGuid
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // Get email configuration from SSM
        const [mailTo, mailFrom, emailTemplateId] = await Promise.all([
          getConfigValue("MAIL_TO"),
          getConfigValue("MAIL_FROM"),
          getConfigValue("SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID"),
        ]);

        const emailRes = await sendDataToSendGrid(
          mailTo,
          mailFrom,
          form_data?.emailAddress,
          emailTemplateId,
          form_data
        );

        // Send Data to success Slack channel
        const slackSuccessWebhookUrl = await getConfigValue(
          "SLACK_SUCCESS_WEBHOOK_URL"
        );
        await sendToSlack(form_data, slackSuccessWebhookUrl);

        //NECESSARY LOGS -> FOR DEBUGGING PURPOSE
        console.log(currentTimestamp());
        console.log("Cloud Migration Lead Data", form_data);
        console.log("HubSpot Response", hubspotResponse);
        console.log("SendGrid Email Response", emailRes);

        return {
          statusCode: 200,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers":
              "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
            "Access-Control-Allow-Methods": "POST,OPTIONS",
          },
          body: JSON.stringify({
            message: "Form submitted successfully.",
            hubspotResponse: hubspotResponse.message,
          }),
        };
      } else {
        console.error("HubSpot Error:", hubspotResponse);

        let formLeadData = form_data;
        formLeadData.page_name = form_data?.secondary_source;
        formLeadData.failed_source = "Hubspot";

        // Get failure email configuration from SSM
        const [failureMailTo, failureMailFrom, failureTemplateId] =
          await Promise.all([
            getConfigValue("MAIL_TO"),
            getConfigValue("MAIL_FROM"),
            getConfigValue("SENDGRID_FAILURE_EMAIL_TEMPLATE_ID"),
          ]);

        const failureEmail = await sendDataToSendGrid(
          failureMailTo,
          failureMailFrom,
          form_data?.emailAddress,
          failureTemplateId,
          formLeadData
        );

        // If HubSpot submission fails -> Send to a failure slack channel
        const slackFailureWebhookUrl = await getConfigValue(
          "SLACK_FAILURE_WEBHOOK_URL"
        );
        await sendToSlack(
          form_data,
          slackFailureWebhookUrl,
          "⚠️ HubSpot Form Submission Failed ⚠️"
        );

        console.log("Failure Email Response", failureEmail);

        // Check if failure mail successfully sent or not
        if (failureEmail.status) {
          console.error(
            `${form_data?.secondary_source} form, failure email send`
          );
        } else {
          console.error(
            `${form_data?.secondary_source} form, failed to send failure email`
          );
        }

        return {
          statusCode: hubspotResponse?.status || 500,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers":
              "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
            "Access-Control-Allow-Methods": "POST,OPTIONS",
          },
          body: JSON.stringify({
            message: "Form submission failed.",
            error: hubspotResponse?.error || "Unknown error from HubSpot",
          }),
        };
      }
    } catch (hubspotError) {
      console.error("HubSpot submission error:", hubspotError);

      let formLeadData = form_data;
      formLeadData.page_name = form_data?.secondary_source;
      formLeadData.failed_source = "Hubspot";

      // Get failure email configuration from SSM
      const [failureMailTo, failureMailFrom, failureTemplateId] =
        await Promise.all([
          getConfigValue("MAIL_TO"),
          getConfigValue("MAIL_FROM"),
          getConfigValue("SENDGRID_FAILURE_EMAIL_TEMPLATE_ID"),
        ]);

      const failureEmail = await sendDataToSendGrid(
        failureMailTo,
        failureMailFrom,
        form_data?.emailAddress,
        failureTemplateId,
        formLeadData
      );

      // Send to failure Slack channel
      const slackFailureWebhookUrl = await getConfigValue(
        "SLACK_FAILURE_WEBHOOK_URL"
      );
      await sendToSlack(
        form_data,
        slackFailureWebhookUrl,
        "⚠️ HubSpot Form Submission Failed ⚠️"
      );

      console.log("Failure Email Response", failureEmail);

      return {
        statusCode: 500,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers":
            "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
          "Access-Control-Allow-Methods": "POST,OPTIONS",
        },
        body: JSON.stringify({
          message: "Internal server error while sending data to HubSpot",
          error: hubspotError.message || hubspotError,
        }),
      };
    }
  } catch (error) {
    console.error("Error parsing request:", error);

    return {
      statusCode: 400,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers":
          "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
        "Access-Control-Allow-Methods": "POST,OPTIONS",
      },
      body: JSON.stringify({
        message: "Invalid request data",
        error: error.message || error,
      }),
    };
  }
};
