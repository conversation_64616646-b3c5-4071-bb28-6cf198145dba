@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorBlack, gray300, colorWhite from variables;

.button,
.link {
  position: relative;
  background-color: colorBlack;
  background-image: linear-gradient(colorBlack, colorBlack),
    linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColor<PERSON>our 75.75%,
      brandColorFive 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;

  font-size: 14px;
  font-weight: 600;
  padding: 8px 14px;
  cursor: pointer;
  line-height: 21px;
  letter-spacing: 0.2px;
  color: colorWhite;
  transition: 0.2s linear;
  min-width: 120px;
  min-height: 40px;
  border: 2px solid transparent;
  /* Required to create the border effect */
  border-radius: 3px;
}


.button:hover {

  box-shadow:
    3px 3px 13px 0px #ffac7d82,
    6px -2px 11px 0px #ff72ae8f,
    -6px 3px 11px 1px #ffbb0057;
}

.innerWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.leftWrapper {
  margin-right: 8px;
}

.rightWrapper {
  margin-left: 8px;
}

.link {
  display: inline-block;
  color: colorWhite;
  text-decoration: none;
}