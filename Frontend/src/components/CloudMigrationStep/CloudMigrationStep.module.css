@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md-820 from breakpoints;

.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 824px;
  margin: 0 auto;
  align-items: flex-start;
  padding-bottom: 40px;
}

.stepWrapper {
  display: flex;
  flex-direction: column;
  gap: 9px;
  align-items: center;
  position: relative;
}

.circle {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 1px solid #646464;
  color: #646464;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 400;
  z-index: 1;
  cursor: not-allowed;
}

.active_circle {
  color: #fff !important;
  border-color: #f05443 !important;
  background-color: #f05443;
  cursor: pointer;
}

.selected_circle {
  color: #fff !important;
  border-color: #f05443 !important;
  background-color: #f05443;
  cursor: pointer;
}

.label {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 139%;
  width: 128px;
  height: 42px;
  text-align: center;
  color: colorBlack;
  margin-bottom: 0;
  cursor: not-allowed;
}

.active_text {
  color: #f05443 !important;
  cursor: pointer;
}

.selected_text {
  color: #f05443 !important;
  cursor: pointer;
}

.line {
  height: 2px;
  width: 121px;
  background-color: #646464;
  position: absolute;
  left: calc(100% + -38px);
  top: 24%;
  z-index: 0;

  @media screen and (max-width: breakpoint-md-820) {
    width: 13.5vw;
    left: 12vw;
  }
}

.line.active {
  background-color: #f05443;
}
