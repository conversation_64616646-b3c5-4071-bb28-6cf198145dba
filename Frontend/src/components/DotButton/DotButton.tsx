import React, { PropsWithChildren } from 'react';

type PropType = PropsWithChildren<
  React.DetailedHTMLProps<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    HTMLButtonElement
  >
>;
export default function DotButton(props: PropType) {
  const { children, ...restProps } = props;

  return (
    // eslint-disable-next-line react/jsx-props-no-spreading
    <button type="button" {...restProps} aria-label="carousel-button">
      {children}
    </button>
  );
}
