@value variables: "@styles/variables.module.css";
@value colorBlack, bodyHeadingS, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, gray900 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-xl-1440 from breakpoints;

.searchContainer {
  min-height: 100vh;
  padding: 5rem 9.375rem;
  color: white;
  background-color: black;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 2.5rem 2rem;
  }

  @media (max-width: breakpoint-sm) {
    padding: 2.5rem 1rem;
  }
}

.searchSection {
  padding: 0;
  color: colorWhite;
  max-width: 1194px;
}

.inputLabel {
  padding-bottom: 10px;
  font-size: 18px;
  font-weight: 400;
  line-height: 168%;
}

.inputContainer {
  position: relative;
}

.searchInput {
  width: 100%;
  padding: 10px 0;
  border: none;
  border-bottom: 2px solid #fff;
  background-color: transparent;
  color: #fff;
  font-size: 18px;
  outline: none;
}

.searchInput::placeholder {
  color: #fff;
  opacity: 0.7;
}

.searchInput::-webkit-search-cancel-button {
  display: none;
}

.searchButton {
  padding: 0;
  position: absolute;
  top: 33%;
  right: 22.5px;
  transform: translateY(-50%);
  cursor: pointer;
  outline: none;
}

.resetButton {
  padding: 0;
  position: absolute;
  top: 33%;
  right: 0px;
  transform: translateY(-50%);
  cursor: pointer;
  outline: none;
}

.searchInput:focus {
  .ais-SearchBox-resetIcon {
    display: none !important;
  }
}

.searchResultsSection {
  max-width: 808px;
}

.searchResultsHeader {
  padding-bottom: 10px;
  display: flex;
  justify-content: space-between;

  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 168%;
  border-bottom: 1px solid #fff;
}

.allResultsTitle {
  font-weight: 600;
}

.resultsFound {
  text-align: right;
}

.resultsCategoryHeader {
  background-color: gray900;
  margin-top: 10px;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 400;
  line-height: 168%;
  border-bottom: 1px solid transparent;
  border-image: linear-gradient(
    93deg,
    #febe10 0%,
    #f47a37 30.56%,
    #f05443 53.47%,
    #d91a5f 75.75%,
    #b41f5e 100%
  );
  border-image-slice: 1;
}

.resultsCategoryTitleWithSorting {
  font-size: 18px;
  font-weight: 600;
  line-height: 168%;
}

.resultsCategorySorting {
  display: flex;
  gap: 5px;
  align-self: center;
  align-items: center;
}

.sortByDropdownDiv:focus-visible {
  outline: none;
}

.sortByDropdownDiv > select:focus-visible {
  outline: none;
}

.sortByDropdownDiv > select {
  min-width: 90px;
  border: none;
  color: white;
  background: black;

  @media (max-width: breakpoint-sm) {
    min-width: auto;
  }
}

.sortByDropdownDiv select option {
  background: white;
  color: black;
  border-radius: 6px !important;
}

.resultsCategoryTitle {
  background-color: gray900;
  margin-top: 10px;
  padding: 10px;
  font-size: 18px;
  font-weight: 600;
  line-height: 168%;
  border-bottom: 1px solid transparent;
  border-image: linear-gradient(
    93deg,
    #febe10 0%,
    #f47a37 30.56%,
    #f05443 53.47%,
    #d91a5f 75.75%,
    #b41f5e 100%
  );
  border-image-slice: 1;
}

.hitContent {
  height: 80px;
  align-items: center;
  display: flex;
  justify-content: space-between;
  text-decoration: none;
  border-bottom: 1px solid #fff;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  @media (max-width: breakpoint-md) {
    padding: 15px 0px;
  }
}

.hitContent:hover {
  .hitArrow {
    opacity: 1;
    transform: translateX(0);
  }
}

.hitContentWrapper {
  max-width: 95%;
}

.searchHitTitle > h5 {
  font-size: 18px;
  font-weight: 400;
  line-height: 168%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: breakpoint-sm) {
    font-size: 14px;
    line-height: normal;
  }
}

.searchHitUrl {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: breakpoint-sm) {
    font-size: 8px;
    line-height: normal;
  }
}

.searchHitDate {
  font-size: inherit;

  @media (max-width: breakpoint-sm) {
    font-size: 10px;
    line-height: normal;
  }
}

.hitArrow {
  opacity: 0;
  min-height: inherit;
  height: inherit;
  background: white;
  transform: translateX(50%);
  transition:
    opacity 0.125s ease-out,
    transform 0.25s ease-out;

  @media (max-width: breakpoint-md) {
    display: none;
  }
}

.searchResultsFooter {
  margin-top: 20px;
  text-align: right;
  font-size: 18px;

  @media (max-width: breakpoint-sm) {
    text-align: left;
  }
}

.noResults {
  padding: 10px 0 10px 0;
}
