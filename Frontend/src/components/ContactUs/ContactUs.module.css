@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, blueFonts, grayFonts, grayBg, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;

@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm-380, breakpoint-md, breakpoint-xl, breakpoint-sm-450, breakpoint-sm from breakpoints;

.mainContainer {
  font-weight: 400;
  color: grayBlueFonts;
  max-width: 1202px;
  height: 1233px;
  margin: 3.75rem auto 5rem auto;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  @media screen and (max-width: breakpoint-xl) {
    height: 100%;
    margin: 0;
    flex-direction: column;
  }
}

.formContainer,
.rightContainer {
  width: 586px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  @media screen and (max-width: breakpoint-xl) {
    width: 100%;
    padding: 2rem 0;
  }

  @media screen and (max-width: breakpoint-md) {
    padding: 0;
  }
}

.formContainer {
  padding: 5rem 0;
  font-size: 18px;
  line-height: 23.04px;
  background-color: grayBg;

  @media screen and (max-width: breakpoint-md) {
    padding: 0;
  }
}

.link {
  text-decoration: none;
}

.title>h1 {
  font-size: 70px;
  font-weight: 700;
  line-height: 70.7px;

  background: linear-gradient(to right,
      brandColorOne,
      brandColorTwo,
      brandColorThree,
      brandColorFour,
      brandColorFive);
  background-clip: text;
  color: transparent;
  margin: 0;

  @media screen and (max-width: breakpoint-sm-450) {
    font-size: 52px;
    font-weight: 600;
    line-height: 57.2px;
  }

  @media screen and (max-width: breakpoint-sm-380) {
    font-size: 40px;
    line-height: 44px;
  }
}

.description {
  margin-top: 1rem;
}

.textBox {
  margin: 0 2rem;

  @media screen and (max-width: breakpoint-xl) {
    margin: auto;
    max-width: 700px;
  }

  @media screen and (max-width: breakpoint-md) {
    display: none;
  }

  @media screen and (max-width: breakpoint-sm-450) {
    margin: 0;
    display: block;
    background-color: colorWhite;
    padding: 1.875rem 1rem;
  }
}

.rightContainer {
  position: relative;
  background-color: colorBlack;
  color: colorWhite;
}

.exceptCarousel {
  max-width: 513px;
  margin: 5rem auto 1.875rem auto;

  @media screen and (max-width: breakpoint-xl) {
    max-width: 700px;
  }

  @media screen and (max-width: breakpoint-md) {
    margin: 3.75rem 1.875rem;
  }

  @media screen and (max-width: breakpoint-sm-450) {
    margin: 3.75rem 1.875rem 0 1.875rem;
  }
}

.flexContainer {
  display: flex;
  flex-direction: column;
  position: relative;

  @media screen and (max-width: breakpoint-md) {
    flex-direction: column-reverse;
    margin-bottom: 1.875rem;
  }
}

.logo {
  position: relative;
  height: auto;
  z-index: 1;
}

.gradientImageTop {
  position: absolute;
  right: 0;
  top: 0;
  pointer-events: none;

  @media screen and (max-width: breakpoint-xl) {
    right: unset;
    left: 0;
    top: -28px;
    rotate: 270deg;
  }

  @media screen and (max-width: breakpoint-sm-450) {
    left: unset;
    right: 0;
    top: 0;
    rotate: 0deg;
  }
}

.gradientImageBottom {
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;

  @media screen and (max-width: breakpoint-xl) {
    display: none;
  }
}

.trustedBy {
  margin-top: 2.8rem;
  font-size: 18px;
  line-height: 22px;
  color: grayFonts;

  @media screen and (max-width: breakpoint-md) {
    margin-top: 0;
  }
}

.companies {
  max-width: 513px;
  opacity: 80%;
  display: flex;
  flex-wrap: wrap;
  column-gap: 1.875rem;

  @media screen and (max-width: breakpoint-md) {
    opacity: 100%;
  }

  @media screen and (max-width: breakpoint-sm) {
    max-width: 242px;
  }
}

.infoContainer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 1.75rem;

  @media screen and (max-width: breakpoint-md) {
    margin-top: 0;
    margin-bottom: 1.875rem;
  }
}

.textCard {
  margin-top: 1.5rem;

  @media screen and (max-width: breakpoint-md) {
    margin-top: 1.875rem;
  }
}

.textCardHeading>h2 {
  font-weight: 400;
  font-size: 18px;
  line-height: 22px;
  color: grayFonts;
}

.content {
  display: flex;
  column-gap: 12px;
  flex-wrap: wrap;

  @media screen and (max-width: breakpoint-md) {
    gap: 10px;
    flex-direction: column;
  }
}

.text {
  margin-top: 2px;
  width: 225px;
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
}

.collab_title {
  font-size: 14px;
  line-height: 24px;
  font-weight: 700;
}

.collabBox {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}