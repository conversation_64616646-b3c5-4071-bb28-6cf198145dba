export interface OurStory {
  id: number;
  description: string;
  service_page_link: string;
  title: string;
  on_hover_bg_image: {
    data: {
      id: number;
      attributes: {
        alternativeText?: string;
        caption?: string;
        createdAt?: string;
        ext?: string;
        formats?: null;
        hash?: string;
        height?: number;
        mime?: string;
        name?: string;
        previewUrl?: null;
        provider?: string;
        provider_metadata?: null;
        size?: number;
        updatedAt?: string;
        url?: string;
        width?: number;
      };
    };
  };
}

export interface cardsType {
  id: number;
  number: number;
  title: string;
  description: string;
}
[];

export interface OurStoryTypes {
  data: {
    id: number;
    title: string;
    attributes: {
      createdAt: string;
      updatedAt: string;
      publishedAt: string;
      our_story: {
        id: number;
        title: string;
        cards: cardsType;
      };
    };
    other_services_card: OurStory[];
  };
}
