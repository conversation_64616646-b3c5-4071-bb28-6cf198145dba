export default function seoSchema(seoData) {
  return {
    title:
      seoData?.title ||
      'AI & ML Solutions | Software Development Partner | Maruti Techlabs',
    description:
      seoData?.description ||
      'Drive business growth with Maruti Techlabs’ secure, scalable, and custom software solutions to automate your processes.',
    alternates: {
      canonical: seoData?.url || 'https://marutitech.com/',
    },
    keywords:
      seoData?.keywords?.keyword ||
      'AI, ML, software development, custom software solutions, automation, business growth',
    openGraph: {
      title: seoData?.title,
      description: seoData?.description,
      type: seoData?.type || 'website',
      url: seoData?.url,
      locale: seoData?.locale || 'en_US',
      siteName: seoData?.site_name || 'Maruti Techlabs',
      images: seoData?.image?.data?.attributes?.url
        ? [
            {
              url: seoData.image.data.attributes.url,
              alt: seoData.title || 'Maruti Techlabs Logo',
            },
          ]
        : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: seoData?.title,
      description: seoData?.description,
      images: seoData?.image?.data?.attributes?.url
        ? [seoData.image.data.attributes.url]
        : [],
      creator: '@MarutiTech',
    },
    other: {
      'application/ld+json': JSON.stringify(getSchemaData(seoData)),
    },
  };
}

function getSchemaData(seoData) {
  const url = seoData?.url || 'https://marutitech.com/';
  const title = seoData?.title || 'Maruti Techlabs';
  const description =
    seoData?.description ||
    'Drive business growth with Maruti Techlabs’ secure, scalable, and custom software solutions to automate your processes.';
  const imageURL = seoData?.image?.data?.attributes?.url || '';

  return {
    '@context': 'https://schema.org',
    '@graph': [
      {
        '@type': 'Organization',
        '@id': `${url}#organization`,
        name: 'Maruti Techlabs',
        url: 'https://marutitech.com/',
        sameAs: [],
      },
      {
        '@type': 'WebSite',
        '@id': `${url}#website`,
        url: 'https://marutitech.com/',
        name: 'Maruti Techlabs',
        publisher: {
          '@id': `${url}#organization`,
        },
        potentialAction: {
          '@type': 'SearchAction',
          target: `${url}?s={search_term_string}`,
          'query-input': 'required name=search_term_string',
        },
      },
      {
        '@type': 'WebPage',
        '@id': `${url}#webpage`,
        url: url,
        inLanguage: 'en-US',
        name: title,
        isPartOf: {
          '@id': `${url}#website`,
        },
        about: {
          '@id': `${url}#organization`,
        },
        image: {
          '@type': 'ImageObject',
          '@id': `${url}#primaryimage`,
          url: imageURL,
          width: 631,
          height: 417,
          caption: 'home-hero-image',
        },
        primaryImageOfPage: {
          '@id': `${url}#primaryimage`,
        },
        datePublished: '2019-03-19T05:53:21+00:00',
        dateModified: '2020-11-02T08:06:30+00:00',
        description: description,
      },
    ],
  };
}
