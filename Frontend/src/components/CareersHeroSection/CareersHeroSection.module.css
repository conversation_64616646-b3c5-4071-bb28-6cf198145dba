@value variables: "@styles/variables.module.css";
@value colorWhite, colorBlack from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-xl-1440 from breakpoints;

.sectionWrapper {
  margin: 0;
  padding: 5rem 9.375rem;
  background-color: colorBlack;
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 2.5rem 2rem;
  }

  @media (max-width: breakpoint-sm) {
    padding: 2.5rem 1rem;
  }
}

.sectionWrapper div {
  padding: 0;
  background-color: colorBlack;
  color: colorWhite;
}

.button {
  width: fit-content;
  padding: 16px 36px;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.videoWrapper {
  width: 100%;
  height: 671px;
  background-color: black;

  @media (max-width: breakpoint-md) {
    height: 450px;
  }

  @media screen and (min-width: breakpoint-xl-1440) {
    max-width: 1192px;
  }
}

.vimeoVideoIframe {
  width: 100%;
  height: 100%;
}
