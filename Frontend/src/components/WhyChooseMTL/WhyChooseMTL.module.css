@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorBlack from variables;

@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-md-585, breakpoint-sm-550, breakpoint-xl-1188, breakpoint-sm from breakpoints;

.sectionWrapper {
  background-color: colorBlack;
  padding-bottom: 40px;
  padding-top: 80px;
}

.whyChooseMTLContentArea {
  width: 100%;
  height: 100%;
  color: white;
}

.headingArea {
  text-align: center;
  display: block;
  width: 100%;
  position: relative;
  z-index: 6;

  @media (max-width: 565px) {
    padding: 40px 20px;
  }
}

.headingArea > div > h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.subHeading {
  font-size: 20px;
  padding-top: 24px;
}

.pointsWrapper {
  max-width: 1169px;
  display: flex;
  justify-content: center;
  margin: 0 auto;
  flex-wrap: wrap;
  position: relative;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 3;
}

.pointsWrapper::before {
  content: '';
  width: 730px;
  height: 730px;
  position: absolute;
  z-index: 2;
}

.pointCard {
  width: 283px;
  height: 192px;
  background-color: colorBlack;
  position: relative;
  z-index: 5;
  overflow: hidden;
  margin-top: 40px;
}

.dividerLine:last-child {
  display: none;
}

.cardBackground {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.pointCardContent {
  padding: 24px;
}

.pointCardTitle > h3 {
  font-size: 18px;
  font-weight: 600;
}

.pointCardTitle > h3 > p {
  margin-bottom: 8px;
}

.pointCardDescription {
  font-size: 14px;
  font-weight: 400;
  position: inherit;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dividerLine {
  height: 192px;
  width: 1px;
  background: linear-gradient(
    to bottom,
    colorBlack,
    colorBlack,
    brandColorOne,
    brandColorTwo,
    brandColorThree,
    brandColorFour,
    brandColorFive
  );
  margin: 40px 9px 0px 9px;
}

@media screen and (max-width: 565px) {
  .pointCard {
    height: auto;
    margin-top: 0;
    background-image: linear-gradient(#000, #000),
      linear-gradient(
        93.12deg,
        #febe10,
        #f47a37 30.56%,
        #f05443 53.47%,
        #d91a5f 75.75%,
        #b41f5e
      );
    background-origin: border-box;
    background-clip: padding-box, border-box;
    border-bottom: 2px solid transparent;
  }

  .pointCard:last-child {
    border-bottom: 0px solid transparent;
  }
}
