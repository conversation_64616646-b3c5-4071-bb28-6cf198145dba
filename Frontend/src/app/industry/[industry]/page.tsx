import { notFound } from 'next/navigation';
import AwardsRecognition from '@components/AwardsRecognition';
import CaseStudyCard from '@components/CaseStudyCard';
import ClutchReviews from '@components/ClutchReviews';
import ContactUsForm from '@components/ContactUsForm';
import CTA from '@components/CTA';
import Faq from '@components/Faq';
import HeroSection from '@components/HeroSection';
import Insights from '@components/Insights';
import ServicesCard from '@components/ServicesCard';
import TabChallenges from '@components/TabChallenges';
import TechStack from '@components/TechStack';
import TrustedPartners from '@components/TrustedPartners';
import WhyChooseMTL from '@components/WhyChooseMTL';
import RichResults from '@components/RichResults';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function generateStaticParams() {
  const industryResponse = await fetchFromStrapi('industries');
  const industry = industryResponse.data.map(res => ({
    industry: res.attributes.slug,
  }));

  return industry;
}

export async function fetchIndustryData(slug: string) {
  const queryString = `filters[slug][$eq]=${slug}&populate=hero_section.image,hero_section.mobile_image,challenges_and_solutions.tab_box.card_image,CTA,CTA2,what_service_we_are_offering.L2ServicesCard.on_hover_bg_image,why_choose_maruti_techlabs.whyChooseMtlCards,industry_awards.awards_box.image,clutch_reviews.review_image,insights.circular_text_image,insights.blogs.heroSection_image,faq.faq_items,form.formFields,form.button,case_study_cards.case_study_relation.preview.preview_background_image&populate=case_study_cards.case_study_relation.hero_section.global_services,tech_stack,tech_stack.tab.logo_url,seo.schema`;
  return await fetchFromStrapi('industries', queryString);
}

async function getTrustedPartnersData() {
  const queryString =
    'populate=trustedPartner.title&populate=trustedPartner.partnersLogo.images';
  return await fetchFromStrapi('trusted-partner', queryString);
}

async function getFormData() {
  const queryString = 'populate=form.formFields&populate=form.button';
  return await fetchFromStrapi('form', queryString);
}

async function getAwardsData() {
  const queryString = 'populate=awards.awards_box.image';
  return await fetchFromStrapi('award', queryString);
}

export async function generateMetadata({
  params,
}: {
  params: { industry: string };
}) {
  const { industry: slug } = params;
  const queryString = `filters[slug][$eq]=${slug}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema`;
  const seoFetchedData = await fetchFromStrapi('industries', queryString);
  const seoData = seoFetchedData?.data[0]?.attributes?.seo;

  // Return the metadata
  return seoSchema(seoData);
}

export default async function Industry({
  params,
}: {
  params: { industry: string };
}) {
  const { industry } = params;
  const industryPageData = await fetchIndustryData(industry);
  const trustedPartnersData = await getTrustedPartnersData();
  const formData = await getFormData();
  const awards = await getAwardsData();

  // Check if industry data exists, otherwise return 404
  if (!industryPageData?.data || industryPageData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {industryPageData?.data[0]?.attributes?.seo && (
        <RichResults data={industryPageData?.data[0]?.attributes?.seo} />
      )}
      {industryPageData?.data[0]?.attributes?.hero_section && (
        <HeroSection
          heroData={industryPageData?.data[0]?.attributes?.hero_section}
          variant="primary"
          industrySlug={industryPageData?.data[0]?.attributes?.slug}
          industryName={industryPageData?.data[0]?.attributes?.pageName}
        />
      )}
      {industryPageData?.data[0]?.attributes?.challenges_and_solutions && (
        <TabChallenges
          tabChallengesData={
            industryPageData?.data[0]?.attributes?.challenges_and_solutions
          }
          variant="industry"
        />
      )}
      {industryPageData?.data[0]?.attributes?.CTA && (
        <CTA
          data={industryPageData?.data[0]?.attributes?.CTA}
          variant="scrollToContactForm"
        />
      )}
      {trustedPartnersData?.data?.attributes?.trustedPartner && (
        <TrustedPartners
          data={trustedPartnersData?.data?.attributes?.trustedPartner}
        />
      )}
      {industryPageData?.data[0]?.attributes?.what_service_we_are_offering && (
        <ServicesCard
          variant="blackSlideCard"
          l2ServiceData={
            industryPageData?.data[0]?.attributes?.what_service_we_are_offering
          }
          background="black"
          variantWhite={false}
        />
      )}
      {industryPageData?.data[0]?.attributes?.case_study_cards && (
        <CaseStudyCard
          case_study={industryPageData?.data[0]?.attributes?.case_study_cards}
        />
      )}
      {industryPageData?.data[0]?.attributes?.why_choose_maruti_techlabs && (
        <WhyChooseMTL
          data={
            industryPageData?.data[0]?.attributes?.why_choose_maruti_techlabs
          }
        />
      )}
      {industryPageData?.data[0]?.attributes?.CTA2 && (
        <CTA
          data={industryPageData?.data[0]?.attributes?.CTA2}
          variant="scrollToContactForm"
        />
      )}
      {industryPageData?.data[0]?.attributes?.tech_stack && (
        <TechStack data={industryPageData?.data[0]?.attributes?.tech_stack} />
      )}
      {awards?.data?.attributes?.awards && (
        <AwardsRecognition
          awardsRecognitionData={awards?.data?.attributes?.awards}
        />
      )}
      {industryPageData?.data[0]?.attributes?.clutch_reviews && (
        <ClutchReviews
          data={industryPageData?.data[0]?.attributes?.clutch_reviews}
        />
      )}
      {industryPageData?.data[0]?.attributes?.insights && (
        <Insights data={industryPageData?.data[0]?.attributes?.insights} />
      )}
      {industryPageData?.data[0]?.attributes?.faq && (
        <Faq faqData={industryPageData?.data[0]?.attributes?.faq} />
      )}
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="Industry"
        />
      )}
    </>
  );
}
