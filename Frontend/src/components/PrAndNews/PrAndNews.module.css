@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-xl-1208, breakpoint-xl-1024, breakpoint-md, breakpoint-sm from breakpoints;

.container {
  padding: 5rem 9.375rem;
  background: url(https://cdn.marutitech.com/Group_5043_dc21af6bd6.svg),
    url(https://cdn.marutitech.com/Group_5041_0468454f87.svg), gray300;
  background-repeat: no-repeat;
  background-position:
    right bottom,
    left top;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.5rem;
  }

  @media (max-width: breakpoint-xl-1208) {
    padding: 5rem 3rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 2.5rem 0.25rem;
  }
}

.title>h2 {
  text-align: center;
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;

  @media (max-width: breakpoint-sm) {
    font-size: 28px;
  }
}

.embla {
  max-width: 125rem;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 20px;
  --slide-size: 25%;

  @media (max-width: breakpoint-md-767) {
    max-width: auto;
    --slide-size: 50%;
  }

  @media (max-width: breakpoint-sm-390) {
    max-width: 320px;
    --slide-size: 50%;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  touch-action: pan-y pinch-zoom;
  user-select: none;
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding: 0 calc(var(--slide-spacing) / 2);
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}

.cardWrapper {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
}

.card {
  margin: 10px 0px;
  width: 283px;
  text-decoration: none;
}

.card:hover .previewBottomContent {
  .arrow_styling {
    display: none;
  }

  .arrow_styling_hover {
    display: flex;
    align-items: center;
  }

  transform: translateX(75%);

  transition: transform 0.4s ease;
}

.card:hover .previewBottomContent {
  transition: transform 0.4s ease;
}

.card:hover:not(:hover) .previewBottomContent {
  transform: translateX(0);
}

.cardPreview {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 283px;
  height: 219px;

  border-radius: 6px;
  overflow: hidden;
}

.previewImage {
  display: block;
  width: 100%;
  height: 100%;
}

.previewBottom {
  padding: 16px 24px;
  width: 283px;
  background-color: colorBlack;
  position: absolute;
  top: 75%;
  left: 0;
  width: 100%;
  height: auto;
  box-sizing: border-box;
}

.previewBottomContent {
  display: flex;
  gap: 4px;
  align-items: center;
  transition: transform 0.25s ease-in-out;
}

.previewBottomContent h3 {
  color: colorWhite;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 144%;
}

.arrow_styling {
  display: flex;
  align-items: center;
}

.arrow_styling_hover {
  display: none;
}

.cardDate {
  margin: 12px 0px 0px 1px;
  padding: 6px 10px;
  width: fit-content;
  position: relative;
  color: colorBlack;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.cardDate::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 6px;
  padding: 1.25px;
  background: linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  -webkit-mask:
    linear-gradient(colorWhite 0 0) content-box,
    linear-gradient(colorWhite 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

.cardTitle>h3 {
  color: colorBlack;
  margin-top: 8px;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 168%;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}