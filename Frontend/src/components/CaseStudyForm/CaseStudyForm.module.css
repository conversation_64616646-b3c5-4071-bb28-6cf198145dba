@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, gray300, gray200, gray800 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-md-767, breakpoint-sm, breakpoint-xl-1024, breakpoint-xl, breakpoint-xl-1400, breakpoint-xl-1440, breakpoint-xl-1024 from breakpoints;

.CaseStudyFormContainer {
  padding: 5rem 9.375rem;
  display: flex;
  gap: 150px;
  justify-content: center;
  background-color: gray300;
  color: colorBlack;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem;
    gap: 0;
    justify-content: space-between;
  }

  @media (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    gap: 30px;
  }

  @media (max-width: breakpoint-md) {
    padding: 2.5rem 2rem;
  }
}

.contentWrapper {
  display: flex;
  width: 60%;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 24px;
  flex-shrink: 0;

  @media (min-width: breakpoint-xl-1440) {
    width: 691px;
  }

  @media (max-width: breakpoint-xl-1024) {
    width: auto;
  }
}

.downloadTitle {
  font-size: 64px;
  font-style: normal;
  font-weight: 600;
  line-height: 120%;
  letter-spacing: -1.28px;

  background: linear-gradient(
    93deg,
    brandColorOne 0%,
    brandColorTwo 30.56%,
    brandColorThree 53.47%,
    brandColorFour 75.75%,
    brandColorFive 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: breakpoint-sm) {
    font-size: 44px;
    font-style: normal;
    font-weight: 600;
    line-height: 118%;
    letter-spacing: -1.76px;
  }
}

.description {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.form {
  display: flex;
  width: 384px;
  padding: 24px;
  flex-direction: column;
  gap: 10px;
  border-radius: 6px;
  background: gray200;

  @media (max-width: breakpoint-sm) {
    width: auto;
  }
}

.formFields {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 160%;
}

.formInput {
  border: 1px solid gray800 !important;
  border-radius: 3px;
  height: 41px;
  border: 0;
  padding: 10px;
}

.formInput:focus-visible {
  border: 0;
  margin: 0;
}

.submitButton {
  background-image: linear-gradient(gray300, gray300),
    linear-gradient(
      93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%
    ) !important;
  margin-top: 10px !important;
  padding: 16px 36px !important;
  color: colorBlack !important;
}

.container_spinner {
  position: relative;
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  border: 2px solid transparent;
  cursor: pointer;
  width: 336px;
  height: 68px;

  background-image: linear-gradient(gray300, gray300),
    linear-gradient(
      93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;

  @media screen and (max-width: breakpoint-md) {
    width: 100%;
  }
}

.spinner {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(
    93deg,
    brandColorOne 0%,
    brandColorTwo 30.56%,
    brandColorThree 53.47%,
    brandColorFour 75.75%,
    brandColorFive 100%
  );

  -webkit-mask-image: radial-gradient(
    circle,
    rgba(0, 0, 0, 0) 55%,
    rgba(0, 0, 0, 1) 60%
  );
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.submitButton > div {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
}

.submitButton::before {
  border-radius: 6px;
  padding: 2px;
}

.error {
  color: rgb(249, 84, 73);
  font-size: 14px;
  font-weight: 600;
}

.ph_number_countries_input_services_page {
  width: 100% !important;
  border: 1px solid gray800 !important;
  border-radius: 3px;
  height: 41px !important;
  padding: 10px;
}

.ph_number_countries_button_services_page {
  border: 1px solid gray800 !important;
  background-color: white !important;
}

.ph_number_countries_dropdown_services_page {
  background-color: white !important;
}

.errorInput {
  border: 1px solid #ff0000 !important;
}

.errorMessages {
  font-weight: 500;
  font-size: 16px;
  line-height: 25.6px;
  color: #ff0000;
}

.errorLabel {
  color: #ff0000;
}

.ph_number_countries_button_services_page > div,
.ph_number_countries_button_services_page > div:hover {
  background-color: white !important;
}
.ph_number_countries_button_services_page > div > div > div {
  border-bottom: none !important;
  border-top: 5px solid black !important;
}
