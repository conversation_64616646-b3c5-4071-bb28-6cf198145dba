import StatisticsCard from './StatisticsCard';

export default {
  title: 'Components/StatisticsCard',
};

const data = {
  data: {
    id: 1,
    attributes: {
      createdAt: '2024-06-05T12:35:35.535Z',
      updatedAt: '2024-06-05T12:38:57.528Z',
      publishedAt: '2024-06-05T12:35:38.268Z',
      Company_Statistics: {
        id: 1,
        Title: 'Company statistics',
        StatisticsCards: [
          {
            id: 1,
            Statistics: 14,
            Suffix: '+',
            DecimalValue: false,
            DecimalAfterNumbersOfDigit: null,
            Separator: null,
            AnimateOnVisible: true,
            Description: 'Years of Experience',
          },
          {
            id: 2,
            Statistics: 4.8,
            Suffix: '/5',
            DecimalValue: true,
            DecimalAfterNumbersOfDigit: 1,
            Separator: null,
            AnimateOnVisible: false,
            Description: 'NPS on Clutch',
          },
        ],
      },
    },
  },
  meta: {},
};

export function StatisticsCardStory() {
  return (
    <div>
      <StatisticsCard
        statisticsCardData={data?.data?.attributes?.Company_Statistics}
      />
    </div>
  );
}
