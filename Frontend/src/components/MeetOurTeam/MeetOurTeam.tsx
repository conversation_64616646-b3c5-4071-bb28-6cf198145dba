'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import Link from 'next/link';
import useEmblaCarousel from 'embla-carousel-react';
import DotButton from '@components/DotButton/DotButton';
import Heading from '@components/Heading';
import useDotButton from '@hooks/useDotButton';
import useMediaQueryState from '@hooks/useMediaQueryState';
import emblastyles from '../../styles/emlaDots.module.css';
import classNames from '@utils/classNames';

import styles from './MeetOurTeam.module.css';
import ImageWithSizing from '@components/ImageWithSizing';

export default function meetOurTeam({
  meetOurTeamData,
  variant,
  variantWhite = true,
}) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    dragFree: true,
    align: 'start',
  });

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const isTablet = useMediaQueryState({
    query: `(max-width: 768px)`,
  });
  return (
    <>
      {variant === 'about-us' &&
        (isTablet ? (
          <Container fluid className={styles.main_container_embla}>
            <div className={styles.inner_container}>
              <Heading
                headingType="h2"
                title={meetOurTeamData.title}
                className={styles.heading}
              />
              <div className={styles.embla}>
                <div className={styles.embla__viewport} ref={emblaRef}>
                  <div className={styles.embla__container}>
                    {meetOurTeamData?.our_people.map((data, index) => (
                      <div className={styles.embla__slide} key={index}>
                        <div className={styles.main_box}>
                          <div className={styles.single_image}>
                            <ImageWithSizing
                              src={data?.image?.data?.attributes}
                              width={380}
                              height={300}
                              alt="background image"
                              className={styles.imageWrapper}
                            />
                          </div>
                          <div className={styles.text_icon_container}>
                            <Link
                              href={data?.link}
                              className={styles.small_icon}
                              target="_blank"
                              prefetch={false}
                            >
                              <Image
                                src={data?.logo?.data?.attributes?.url}
                                width={24}
                                height={24}
                                alt="logo image"
                              />
                            </Link>
                            <div className={styles.title_description}>
                              <Heading
                                headingType="h3"
                                title={data?.title}
                                className={styles.card_title}
                              />
                              <div
                                className={styles.card_description}
                                dangerouslySetInnerHTML={{
                                  __html: data?.description,
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className={styles.embla__controls}>
                  <div className={emblastyles.embla__dots}>
                    {scrollSnaps.length > 1 &&
                      scrollSnaps.map((_, index) => (
                        <DotButton
                          key={index}
                          onClick={() => onDotButtonClick(index)}
                          className={
                            index === selectedIndex
                              ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                              : variantWhite
                                ? classNames(
                                    emblastyles.embla__dot,
                                    emblastyles.embla__dot_bg_white,
                                  )
                                : emblastyles.embla__dot
                          }
                        />
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </Container>
        ) : (
          <Container fluid className={styles.main_container}>
            <div className={styles.inner_container}>
              <Heading
                headingType="h2"
                title={meetOurTeamData.title}
                className={styles.heading}
              />
              <div className={styles.card_box_container}>
                {meetOurTeamData?.our_people.map(data => (
                  <div className={styles.main_box} key={data?.id}>
                    <div className={styles.single_image} key={data?.id}>
                      <ImageWithSizing
                        src={data?.image?.data?.attributes}
                        width={380}
                        height={300}
                        alt="background image"
                        className={styles.imageWrapper}
                      />
                    </div>
                    <div className={styles.text_icon_container}>
                      <Link
                        href={data?.link}
                        className={styles.small_icon}
                        target="_blank"
                        prefetch={false}
                      >
                        <Image
                          src={data?.logo?.data?.attributes?.url}
                          width={24}
                          height={24}
                          alt="logo image"
                        />
                      </Link>
                      <div className={styles.title_description}>
                        <Heading
                          headingType="h3"
                          title={data?.title}
                          className={styles.card_title}
                        />
                        <div
                          className={styles.card_description}
                          dangerouslySetInnerHTML={{
                            __html: data?.description,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Container>
        ))}
      {variant === 'partners' && (
        <Container fluid className={styles.main_container_embla}>
          <div className={styles.inner_container}>
            <Heading
              headingType="h2"
              title={meetOurTeamData.title}
              className={styles.heading}
            />
            <div className={styles.embla}>
              <div className={styles.embla__viewport} ref={emblaRef}>
                <div className={styles.embla__container}>
                  {meetOurTeamData?.our_people.map((data, index) => (
                    <div className={styles.embla__slide} key={index}>
                      <div className={styles.main_box}>
                        <div className={styles.single_image}>
                          <ImageWithSizing
                            src={data?.image?.data?.attributes}
                            width={380}
                            height={300}
                            alt="background image"
                            className={styles.imageWrapper}
                          />
                        </div>
                        <div className={styles.text_icon_container}>
                          <Link
                            href={data?.link}
                            className={styles.small_icon}
                            target="_blank"
                            prefetch={false}
                          >
                            <Image
                              src={data?.logo?.data?.attributes?.url}
                              width={24}
                              height={24}
                              alt="logo image"
                            />
                          </Link>
                          <div className={styles.title_description}>
                            <Heading
                              headingType="h3"
                              title={data?.title}
                              className={styles.card_title}
                            />
                            <div
                              className={styles.card_description}
                              dangerouslySetInnerHTML={{
                                __html: data?.description,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className={styles.embla__controls}>
                <div className={emblastyles.embla__dots}>
                  {scrollSnaps.length > 1 &&
                    scrollSnaps.map((_, index) => (
                      <DotButton
                        key={index}
                        onClick={() => onDotButtonClick(index)}
                        className={
                          index === selectedIndex
                            ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                            : variantWhite
                              ? classNames(
                                  emblastyles.embla__dot,
                                  emblastyles.embla__dot_bg_white,
                                )
                              : emblastyles.embla__dot
                        }
                      />
                    ))}
                </div>
              </div>
            </div>
          </div>
        </Container>
      )}
    </>
  );
}
