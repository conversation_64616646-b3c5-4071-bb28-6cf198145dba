import React from 'react';
import { Container } from 'react-bootstrap';
import CircularButtonWithArrow from '@components/CircularButtonWithArrow';
import Link from 'next/link';
import Image from 'next/image';
import style from './blogBanner.module.css';

const Banner = ({ blogCaseStudySuggestionData }) => {
  return (
    <>
      <Container fluid className={style.bannerPrimaryWithButton}>
        <Image
          src={blogCaseStudySuggestionData?.cover_image?.data?.attributes?.url}
          alt={blogCaseStudySuggestionData?.title}
          fill
          className={style.background_image}
        />
        <div className={style.categoryAndTitle}>
          <div className={style.category}>Case Study</div>
          <div className={style.title}>
            {blogCaseStudySuggestionData?.title}
          </div>
        </div>
        <Link
          target="_blank"
          href={blogCaseStudySuggestionData?.link}
          passHref
          prefetch={false}
        >
          <CircularButtonWithArrow />
        </Link>
      </Container>
    </>
  );
};

export default Banner;
