@value variables: "@styles/variables.module.css";
@value colorWhite, colorBlack from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl-2000, breakpoint-md from breakpoints;

.main_conatiner {
  padding: 80px 124px;
  background: colorBlack;

  @media screen and (min-width: breakpoint-xl-2000) {
    padding: 80px 150px;
  }

  @media screen and (max-width: breakpoint-md) {
    padding: 80px 32px;
  }
}

.inner_container {
  display: flex;
  gap: 100px;

  @media screen and (max-width: breakpoint-md) {
    flex-direction: column;
  }
}

.title>h2 {
  color: colorWhite;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  /* 56px */
  letter-spacing: -0.8px;
}

.description {
  color: colorWhite;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.text_container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 24px;
  margin: 0;
}

.image_conatiner {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image_wrapper {
  position: relative;
  width: 230px;
  height: 392px;
  overflow: visible;
}

.image_wrapper::before,
.image_wrapper::after {
  content: '';
  position: absolute;
  width: 315px;
  height: 245px;
  background: url('https://cdn.marutitech.com/top_left_gradient_7894cfdb91.svg') no-repeat;
  background-size: cover;
  z-index: 2;
}

.image_wrapper::before {
  top: -16%;
  left: -58%;
}

.image_wrapper::after {
  bottom: -8%;
  right: -28%;
  transform: rotate(180deg);
}

.image {
  position: relative;
  z-index: 3;
}