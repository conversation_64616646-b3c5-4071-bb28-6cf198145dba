@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-lg, breakpoint-md, breakpoint-sm, breakpoint-sm-427 from breakpoints;

.container {
    margin: 0;
    padding: 4rem 9.375rem;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.80) 0%, rgba(255, 255, 255, 0.80) 100%), linear-gradient(93deg, #FEBE10 0%, #F47A37 30.56%, #F05443 53.47%, #D91A5F 75.75%, #B41F5E 100%);
    font-size: 26px;
    font-weight: 600;

    @media (max-width: breakpoint-xl-1440) {
        padding: 4rem 7.75rem;
    }

    @media (max-width: breakpoint-md) {
        padding: 4rem 2rem;
    }
}

.title {
    padding-top: 10px;
    text-align: center;
    font-size: 22px;
    font-style: normal;
    font-weight: 400;
    line-height: 160%;
}