@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-md-767, breakpoint-md-769, breakpoint-lg, breakpoint-xl-1024, breakpoint-xl, breakpoint-xl-1309, breakpoint-xl-1400, breakpoint-xl-1440, breakpoint-sm-390, breakpoint-sm-550 from breakpoints;

.container {
  padding: 5rem 9rem;
  background-color: gray300;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 0;
  }
}

.heading>h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 140%;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
  }
}

.embla {
  max-width: 102rem;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 20px;
  --slide-size: 33.3%;

  @media (max-width: breakpoint-xl-1440) {
    max-width: 74.5rem;
    --slide-size: 25%;
  }

  @media (max-width: breakpoint-md-767) {
    max-width: auto;
    --slide-size: 100%;
  }

  @media (max-width: breakpoint-sm-390) {
    max-width: 320px;
    --slide-size: 100%;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  justify-content: space-between;
  gap: 10;
  touch-action: pan-y pinch-zoom;
  /* margin-left: calc(var(--slide-spacing) * -1); */
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding: 0 calc(var(--slide-spacing) / 2);
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}

.cardWrapper {
  color: colorWhite;
  padding: 40px 0;
}

.card {
  display: flex;
  padding: 40px 0;
  flex-direction: column;
  gap: 10px;
  width: 384px;
  height: 256px;
  border-radius: 6px;
  color: colorBlack;
  background-color: colorWhite;
  background-repeat: no-repeat;
  padding: 1.5rem;
  text-align: left;
  user-select: none;
  min-height: 341px;

  @media (max-width: breakpoint-sm-390) {
    width: 300px;
    height: 422px;
  }
}

@media (min-width: breakpoint-md) {
  .card:hover {
    .hrLine {
      width: 66%;
    }
  }
}

.cardHeader {
  display: flex;
}

.card__number {
  position: relative;
  padding: 2px 6px;
  text-align: center;

  font-size: 16px;
  font-weight: 400;
  font-style: normal;
  line-height: normal;
  text-wrap-mode: nowrap;
}

.card__number::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 3px;
  padding: 1.5px;
  background: linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  -webkit-mask:
    linear-gradient(colorWhite 0 0) content-box,
    linear-gradient(colorWhite 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

.hrLine {
  width: 0%;
  height: 2px;
  align-self: center;
  background: linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  transition: width 0.3s ease;
}

.card__title>h3 {
  padding-bottom: 1rem;

  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 148%;
}

.card__description {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}