'use client';

import style from './EmployeeTestimonial.module.css';
import Heading from '@components/Heading';
import Image from 'next/image';
import { EmblaOptionsType } from 'embla-carousel';
import useDotButton from '@hooks/useDotButton';
import DotButton from '@components/DotButton/DotButton';
import useEmblaCarousel from 'embla-carousel-react';
import { Container } from 'react-bootstrap';
import classNames from '@utils/classNames';
import Autoplay from 'embla-carousel-autoplay';

import emblastyles from '../../styles/emlaDots.module.css';

export default function EmployeeTestimonial({
  EmployeeTestimonialData,
  variantWhite = true,
}) {
  const OPTIONS: EmblaOptionsType = {
    align: 'start',
    dragFree: true,
  };
  const [emblaRef, emblaApi] = useEmblaCarousel(OPTIONS, [
    Autoplay({
      playOnInit: true,
      delay: 5000,
    }),
  ]);
  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  return (
    <Container fluid className={style.sectionWrapper}>
      <div className={style.content}>
        <Heading
          className={style.title}
          title={EmployeeTestimonialData.title}
          headingType="h2"
        />
        <div
          className={style.description}
          dangerouslySetInnerHTML={{
            __html: EmployeeTestimonialData.description,
          }}
        />
      </div>
      <div className={style.embla}>
        <div className={style.embla__viewport} ref={emblaRef}>
          <div className={style.embla__container}>
            {EmployeeTestimonialData?.employee_box?.map((data, index) => (
              <div className={style.embla__slide} key={index}>
                <div className={style.embla__slide__number} key={index}>
                  <div
                    className={style.box_description}
                    dangerouslySetInnerHTML={{
                      __html: data.box_description,
                    }}
                  />
                  <div className={style?.emp_section}>
                    <Image
                      className={style.image}
                      src={data?.emp_image?.data?.attributes?.url}
                      width={87}
                      height={87}
                      alt={data?.emp_image?.data?.attributes?.alternativeText}
                    />
                    <div className={style.emp_title_desc_container}>
                      <div className={style?.emp_title}>{data?.emp_title}</div>
                      <div className={style?.emp_desc}>
                        {data?.emp_description}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className={style.embla__controls}>
          <div className={emblastyles.embla__dots}>
            {scrollSnaps.length > 1 &&
              scrollSnaps.map((_, index) => (
                <DotButton
                  key={index}
                  onClick={() => onDotButtonClick(index)}
                  className={
                    index === selectedIndex
                      ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                      : variantWhite
                        ? classNames(
                            emblastyles.embla__dot,
                            emblastyles.embla__dot_bg_white,
                          )
                        : emblastyles.embla__dot
                  }
                />
              ))}
          </div>
        </div>
      </div>
    </Container>
  );
}
