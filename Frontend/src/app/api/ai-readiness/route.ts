import { NextResponse } from 'next/server';
import sendDataToHubspot from 'common/sendDataToHubSpot';
import sendDataToSendGrid from 'common/sendDataToSendGrid';
import currentTimestamp from 'common/currentTimestamp';
import sendToSlack from 'common/sendDataToSlack';

export async function POST(req: Request) {
  try {
    const form_data = await req.json();

    const formFields = [
      { name: 'firstname', value: form_data?.firstName ?? '' },
      { name: 'lastname', value: form_data?.lastName ?? '' },
      { name: 'email', value: form_data?.emailAddress ?? '' },
      { name: 'company', value: form_data?.companyName },
      { name: 'phone', value: form_data?.phoneNumber ?? '' },
      { name: 'city', value: form_data?.city ?? '' },
      { name: 'country', value: form_data?.country ?? '' },
      { name: 'ip_address', value: form_data?.ip_address ?? '' },
      { name: 'ga_4_userid', value: form_data?.ga_4_userid },
      { name: 'clarity_link', value: form_data?.clarity ?? '' },
      { name: 'source', value: form_data?.secondary_source ?? 'HomePage' },
      { name: 'source_url', value: form_data?.url ?? '' },
      { name: 'utm_campaign', value: form_data?.utm_campaign ?? '' },
      { name: 'utm_source', value: form_data?.utm_source ?? '' },
      { name: 'utm_medium', value: form_data?.utm_medium ?? '' },
      { name: 'referrer', value: form_data?.referrer ?? '' },
      { name: 'consent', value: form_data?.consent ?? '' },
      {
        name: 'do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_',
        value:
          form_data?.do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_ ??
          '',
      },
      {
        name: 'how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_',
        value:
          form_data?.how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_ ??
          '',
      },
      {
        name: 'do_you_have_budget_allocated_for_your_ai_project_',
        value:
          form_data?.do_you_have_budget_allocated_for_your_ai_project_ ?? '',
      },
      {
        name: 'do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_',
        value:
          form_data?.do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_ ??
          '',
      },
      {
        name: 'which_of_the_below_db_tools_do_you_currently_use_',
        value:
          form_data?.which_of_the_below_db_tools_do_you_currently_use_ ?? '',
      },
      {
        name: 'is_the_relevant_data_for_the_ai_project_available_and_accessible_',
        value:
          form_data?.is_the_relevant_data_for_the_ai_project_available_and_accessible_ ??
          '',
      },
      {
        name: 'do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__',
        value:
          form_data?.do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__ ??
          '',
      },
      {
        name: 'how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib',
        value:
          form_data?.how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib ??
          '',
      },
      {
        name: 'does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_',
        value:
          form_data?.does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_ ??
          '',
      },
      {
        name: 'do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_',
        value:
          form_data?.do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_ ??
          '',
      },
      {
        name: 'do_you_have_risk_management_strategies_in_place_for_the_ai_project_',
        value:
          form_data?.do_you_have_risk_management_strategies_in_place_for_the_ai_project_ ??
          '',
      },
      {
        name: 'do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions',
        value:
          form_data?.do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions ??
          '',
      },
      {
        name: 'strategy___leadership',
        value: form_data?.strategy___leadership ?? '',
      },
      {
        name: 'talent___skills',
        value: form_data?.talent___skills ?? '',
      },
      {
        name: 'data_readiness___infrastructure',
        value: form_data?.data_readiness___infrastructure ?? '',
      },
      {
        name: 'impact_evaliation',
        value: form_data?.impact_evaliation ?? '',
      },
      {
        name: 'execution___monitoring',
        value: form_data?.execution___monitoring ?? '',
      },
      {
        name: 'average_of_all_score',
        value: form_data?.average_of_all_score ?? '',
      },
    ];

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_HUBSPOT_API_KEY}`,
      },
    };

    try {
      //Send Data to HubSpot
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        process.env.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID,
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // //Send Data to SendGrid if HubSpot submission is successful !!!

        const emailRes = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID,
          form_data,
        );

        //Send Data to success slack channel
        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
        );

        //NECESSARY LOGS -> FOR DEBUGGING PURPOSE
        console.log(currentTimestamp());
        console.log('Lead Data', form_data);
        console.log('HubSpot Response', hubspotResponse);
        console.log('SendGrid Email Response', emailRes);
        // console.log('------------------------------------');

        return NextResponse.json(
          {
            message: 'Form submitted successfully.',
            hubspotResponse: hubspotResponse.message,
          },
          { status: 200 },
        );
      } else {
        console.error('HubSpot Error:', hubspotResponse);

        //If HubSpot POST API fails -> send (Error while sending form data to HubSpot) failure email
        let formLeadData = form_data;
        formLeadData.page_name = form_data?.secondary_source;
        formLeadData.failed_source = 'Hubspot';

        const failureEmail = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID,
          formLeadData,
        );

        // If HubSpot submission fails -> Send to a failure slack channel

        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
          '⚠️ HubSpot Form Submission Failed ⚠️',
        );

        //Check if failure mail successfully sent or not
        if (failureEmail.status) {
          console.error(
            `${form_data?.secondary_source} form, failure email send`,
          );
        } else {
          console.error(
            `${form_data?.secondary_source} form, failed to send failure email`,
          );
        }

        return NextResponse.json(
          {
            message: 'Form submission failed.',
            error: hubspotResponse?.error || 'Unknown error from HubSpot',
          },
          { status: hubspotResponse?.status || 500 },
        );
      }
    } catch (error) {
      console.error('Error sending to HubSpot:', error);
      return NextResponse.json(
        {
          message: 'Internal server error while sending data to HubSpot',
          error: error.message || error,
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error parsing request:', error);
    return NextResponse.json(
      { message: 'Invalid request data', error: error.message || error },
      { status: 400 },
    );
  }
}
