@value variables: "@styles/variables.module.css";
@value colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-xl-1440 from breakpoints;

.sectionWrapper {
  margin: 0;
  padding: 0 0 80px 0;
  display: flex;
  flex-direction: column;
  gap: 40px;
  background: gray300;

  @media screen and (max-width: breakpoint-md) {
    padding: 0 0 40px 0;
  }
}

.content {
  padding: 5rem 9.375rem 0 9.375rem;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem 0 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 2.5rem 2rem 0 2rem;
  }

  @media (max-width: breakpoint-sm) {
    padding: 2.5rem 1rem 0 1rem;
  }
}

.title>h2 {

  color: colorBlack;
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
  text-align: center;
}

.description {
  color: colorBlack;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  text-align: center;
}

.emp_title {
  color: #383838;

  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 148%;
  /* 150% */
}

.emp_desc {
  color: #262531;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.embla {
  max-width: 100%;
  margin: auto;
  --slide-height: auto;
  --slide-spacing: 20px;
  --slide-size: auto;

}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
}

.embla__slide:first-child {
  margin-left: 9.375rem;

  @media (max-width: breakpoint-xl-1440) {
    margin-left: 7.75rem;
  }

  @media screen and (max-width: breakpoint-md) {
    margin-left: 2rem;
  }

  @media screen and (max-width: breakpoint-sm) {
    margin-left: 1rem;
  }
}

.embla__slide:last-child {
  margin-right: 9.375rem;

  @media (max-width: breakpoint-xl-1440) {
    margin-right: 7.75rem;
  }

  @media screen and (max-width: breakpoint-md) {
    margin-right: 2rem;
  }

  @media screen and (max-width: breakpoint-sm) {
    margin-right: 1rem;
  }
}

.embla__slide__number {
  display: flex;
  background: colorWhite;
  height: var(--slide-height);
  padding: 24px;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: 30px;
  max-width: 628px;
  border-radius: 6px;
  border: 2px solid #e4e4e4;

  @media screen and (max-width: breakpoint-md) {
    padding: 16px;
    max-width: 398px;
  }

  @media screen and (max-width: 400px) {
    max-width: 340px;
  }
}

.image {
  width: fit-content;
  border-radius: 50%;
}

.emp_section {
  display: flex;
  gap: 20px;
}

.emp_title_desc_container {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}

.box_description {
  color: #262531;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}