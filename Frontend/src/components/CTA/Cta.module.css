@value variables: "@styles/variables.module.css";
@value colorBlack, bodyHeadingS, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md-769, breakpoint-sm-550, breakpoint-sm-320, breakpoint-sm from breakpoints;

.ctaContainer {
  background-color: colorBlack;
  height: 100%;
  padding: 80px 0px;

  @media (max-width: breakpoint-md-769) {
    padding: 40px 0px;
    height: 100%;
  }

  @media (max-width: breakpoint-sm-550) {
    padding: 40px 0px;
    height: 100%;
  }

  @media (max-width: breakpoint-sm-320) {
    padding: 20px 0px;
    height: 100%;
  }
}

.ctaWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;

  @media (max-width: breakpoint-md-769) {
    flex-direction: column;
    text-align: center;
  }
}

.ctaHeading {
  display: block;
  width: 872px;

  @media (max-width: breakpoint-md-769) {
    margin-bottom: 24px;
    width: 100%;
  }
}

.ctaHeading>h2,
.ctaHeading>h3 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.btn {
  min-width: 165px !important;
  height: 64px !important;
  font-size: bodyHeadingS !important;
}

.downloadLinkWrapper {
  position: relative;
  text-decoration: none;
  transition: 0.2s linear;
}

.downloadLinkWrapper::before {
  content: '';
  position: absolute;
  top: 0px;
  left: -1px;
  right: -1px;
  bottom: 0px;
  border-radius: 3px;
  padding: 2.5px;
  /* This is the width of the border */
  background: linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  -webkit-mask:
    linear-gradient(colorWhite 0 0) content-box,
    linear-gradient(colorWhite 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

.downloadLinkWrapper:hover {
  box-shadow:
    3px 3px 13px 0px #ffac7d82,
    6px -2px 11px 0px #ff72ae8f,
    -6px 3px 11px 1px #ffbb0057;
}

.downloadLink {
  width: 165px;
  height: 64px;
  font-size: bodyHeadingS;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
  text-align: center;
  align-content: center;
}