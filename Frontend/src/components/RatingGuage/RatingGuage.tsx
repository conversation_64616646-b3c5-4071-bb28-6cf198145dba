'use client';

import React from 'react';
import GaugeComponent from 'react-gauge-component';
import styles from './RatingGuage.module.css';

const RatingGauge = ({ percentage, tag_list, tag_color }) => {
  const value = Math.max(0, Math.min(percentage, 100));

  // Determine tag and color based on percentage
  const getTagData = score => {
    if (score <= 30) return { tag: tag_list[0].name, color: tag_color[0] };
    if (score <= 60) return { tag: tag_list[1].name, color: tag_color[1] };
    if (score <= 85) return { tag: tag_list[2].name, color: tag_color[2] };
    return { tag: tag_list[3].name, color: tag_color[3] };
  };

  const { tag, color } = getTagData(value);

  // Prepare arc ranges
  const arcConfig = [
    { limit: 30, color: tag_color[0], label: '0–30' },
    { limit: 60, color: tag_color[1], label: '31–60' },
    { limit: 85, color: tag_color[2], label: '61–85' },
    { limit: 100, color: tag_color[3], label: '86–100' },
  ];

  return (
    <div className={styles.gauge_container}>
      <GaugeComponent
        type="semicircle"
        arc={{
          width: 0.2,
          padding: 0.005,
          cornerRadius: 5,
          subArcs: arcConfig.map(arc => ({
            limit: arc.limit,
            color: arc.color,
            showTick: true,
            label: arc.label,
          })),
        }}
        pointer={{
          color: '#000000',
          baseColor: '#ffff',
          length: 0.7,
          width: 12,
        }}
        className={styles.gauge}
        value={value}
        labels={{
          valueLabel: { formatTextValue: () => '' }, // hide numeric percentage
        }}
      />

      <div className={styles.result_text} style={{ backgroundColor: color }}>
        {tag}: {Math.round(value)}%
      </div>
    </div>
  );
};

export default RatingGauge;
