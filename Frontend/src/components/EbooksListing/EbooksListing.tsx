'use client';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import { useState } from 'react';
import styles from './EbooksListing.module.css';
import Link from 'next/link';
import Heading from '@components/Heading';
import ReactPaginate from 'react-paginate';

export default function EbooksListing({ boxData }: any) {
  ///// pagination ///////////
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 6;

  const handlePageChange = selectedPage => {
    setCurrentPage(selectedPage.selected); // ReactPaginate gives selected as object {selected: pageNumber}
  };

  const pageCount = Math.ceil(boxData?.length / itemsPerPage);
  const indexOfLastItem = (currentPage + 1) * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedData = boxData?.slice(indexOfFirstItem, indexOfLastItem); // Paginate the filtered data

  return (
    <>
      <Container fluid className={styles.container}>
        <div className={styles.cardWrapper}>
          {paginatedData.map((data, index) => (
            <Link
              href={data?.attributes?.preview?.link}
              key={index}
              className={styles.link}
              prefetch={false}
            >
              <div className={styles.card_preview}>
                <Image
                  loading="eager"
                  priority
                  key={
                    data?.attributes?.preview?.preview_image?.data?.attributes
                      ?.url
                  }
                  src={
                    data?.attributes?.preview?.preview_image?.data?.attributes
                      ?.format?.small?.url ||
                    data?.attributes?.preview?.preview_image?.data?.attributes
                      ?.formats?.small?.url ||
                    data?.attributes?.preview?.preview_image?.data?.attributes
                      ?.format?.medium?.url ||
                    data?.attributes?.preview?.preview_image?.data?.attributes
                      ?.formats?.medium?.url ||
                    data?.attributes?.preview?.preview_image?.data?.attributes
                      ?.url
                  }
                  alt={
                    data?.attributes?.preview?.preview_image?.data?.attributes
                      ?.alternativeText
                  }
                  className={styles.previewImage}
                  width={384}
                  height={220}
                />
                <Heading
                  headingType="h3"
                  title={data?.attributes?.preview?.title}
                  className={styles.card_title}
                />
              </div>
            </Link>
          ))}
        </div>

        {/* Pagination Controls using ReactPaginate */}
        <div className={styles.paginationContainer}>
          <ReactPaginate
            previousLabel={'<'}
            nextLabel={'>'}
            pageCount={pageCount}
            onPageChange={handlePageChange}
            containerClassName={styles.pagination}
            previousLinkClassName={styles.paginationLink}
            nextLinkClassName={styles.paginationLink}
            disabledClassName={styles.paginationDisabled}
            activeClassName={styles.paginationActive}
          />
        </div>
      </Container>
    </>
  );
}
