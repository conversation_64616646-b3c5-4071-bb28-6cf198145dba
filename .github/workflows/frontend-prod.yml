name: Deploy Static Next.js Site to S3 with CloudFront Invalidation - Production

on:
  push:
    branches:
      - main
    paths:
      - Frontend/**
      - .github/workflows/frontend-prod.yml
  workflow_dispatch:  # Allow manual trigger from GitHub UI

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      AWS_REGION: ap-south-1
      S3_BUCKET_NAME: mtl-site-prod-environment
      SSM_PARAMETER_PATH: "/production/maruti-site/env"

    steps:

      # Determine display name for the author
      - name: Check if author needs to be replaced
        id: check_author
        run: |
          if [ "${{ github.actor }}" = "24072012" ]; then
            echo "author=Strapi" >> $GITHUB_OUTPUT
          else
            echo "author=${{ github.actor }}" >> $GITHUB_OUTPUT
          fi
        shell: bash

      # Notify Slack that deployment has started
      - name: Notify Deployment startup to Slack
        uses: slackapi/slack-github-action@v1.23.0
        with:
          channel-id: 'C05EU6F4EG6'
          payload: |
            {
              "text":  "_Event: Deployment Started_ :arrows_counterclockwise: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Frontend \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "_Event: Deployment Started_ :arrows_counterclockwise: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Frontend \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

      - name: Checkout Code
        uses: actions/checkout@v3

      # Set up Node.js environment
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Configure AWS credentials using GitHub secrets
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      # Fetch and load environment variables securely from AWS SSM Parameter Store
      - name: Fetch env vars from SSM Parameter Store
        run: |
          # Fetch the single parameter (stored as JSON)
          json=$(aws ssm get-parameter \
            --name "${{ env.SSM_PARAMETER_PATH }}" \
            --with-decryption \
            --query 'Parameter.Value' \
            --output text)

          # Parse JSON and export as env vars
          echo "$json" | jq -r 'to_entries[] | "\(.key)=\(.value)"' |
          while IFS='=' read -r key value; do
            clean_key=$(printf '%s' "$key" | tr -d '\r\n')
            clean_val=$(printf '%s' "$value" | tr -d '\r\n')
            echo "::add-mask::$clean_val"
            echo "${clean_key}=${clean_val}" >> $GITHUB_ENV

            # Echo for debug (Masked)
            echo "Loaded env: $clean_key"
          done

      # Install project dependencies
      - name: Install Dependencies
        run: npm install
        working-directory: Frontend

      # Build and export the static Next.js site and generate updated sitemap/robots
      - name: Build and Export Static Site
        working-directory: Frontend
        env:
          NEXT_PUBLIC_SITE_URL: ${{ env.NEXT_PUBLIC_SITE_URL }}
        run: |
          echo "Cleaning old sitemap and robots.txt files"
          rm -f out/sitemap.xml out/robots.txt || true
          rm -f public/sitemap.xml public/robots.txt || true

          echo "Building with site URL: $NEXT_PUBLIC_SITE_URL"
          npm run build
          npx next-sitemap
          cp public/sitemap.xml out/sitemap.xml
          cp public/robots.txt out/robots.txt

          echo "Sitemap generation completed for: $NEXT_PUBLIC_SITE_URL"

          # Verify sitemap was generated correctly
          if [ -f "out/sitemap.xml" ]; then
            echo "Sitemap preview (first 5 URLs):"
            head -10 out/sitemap.xml | grep -o 'https://[^<]*' | head -5 || echo "Could not extract URLs from sitemap"
          else
            echo "ERROR: Sitemap file not found!"
            exit 1
          fi

      # Upload the built static files to S3 bucket
      - name: Upload build to S3 using AWS CLI
        run: |
           aws s3 sync out s3://${{ env.S3_BUCKET_NAME }} --delete
        working-directory: Frontend
        env:
          AWS_ACCESS_KEY_ID:        ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY:    ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION:               ${{ env.AWS_REGION }}

      # Invalidate CloudFront cache to serve updated content immediately
      - name: Invalidate CloudFront Cache
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.cloudfront_id_production }} \
            --paths "/*"
        env:
          AWS_ACCESS_KEY_ID:        ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY:    ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION:               ${{ env.AWS_REGION }}

      # Notify Slack when the deployment completes successfully
      - name: Notify Deployment completion to Slack
        uses: slackapi/slack-github-action@v1.23.0
        with:
          channel-id: 'C05EU6F4EG6'
          payload: |
            {
              "text":  "_Event: Deployment Completed_ :rocket: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Frontend \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "_Event: Deployment Completed_ :rocket: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Frontend \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

      # Notify Slack if any previous step in the workflow fails
      - name: Notify Deployment failure to Slack
        if: failure()
        uses: slackapi/slack-github-action@v1.23.0
        with:
          channel-id: 'C05EU6F4EG6'
          payload: |
            {
              "text":  "_Event: Deployment Failed_ :rotating_light: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Frontend \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "_Event: Deployment Failed_ :rotating_light: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Deployment Type:* Frontend \n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}